#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
百度OCR代理服务器 (Python版本)
用于解决CORS跨域问题

使用方法：
1. 安装依赖：pip install flask flask-cors requests
2. 运行：python baidu_proxy.py
3. 代理服务器将在 http://localhost:3001 启动
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import requests
import json
import logging

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 启用CORS

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置
PORT = 3001
DEBUG = True

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'ok',
        'message': '百度OCR代理服务运行正常',
        'version': '1.0.0'
    })

@app.route('/api/baidu/token', methods=['POST'])
def get_baidu_token():
    """获取百度访问令牌的代理接口"""
    try:
        data = request.get_json()
        
        if not data or 'apiKey' not in data or 'secretKey' not in data:
            return jsonify({'error': '缺少必要参数：apiKey 和 secretKey'}), 400
        
        api_key = data['apiKey']
        secret_key = data['secretKey']
        
        logger.info(f'获取百度访问令牌，API Key: {api_key[:8]}...')
        
        # 调用百度API获取访问令牌
        response = requests.post(
            'https://aip.baidubce.com/oauth/2.0/token',
            data={
                'grant_type': 'client_credentials',
                'client_id': api_key,
                'client_secret': secret_key
            },
            timeout=10
        )
        
        result = response.json()
        
        if 'access_token' in result:
            logger.info('成功获取百度访问令牌')
        else:
            logger.error(f'获取令牌失败: {result}')
        
        return jsonify(result)
        
    except requests.exceptions.RequestException as e:
        logger.error(f'网络请求失败: {e}')
        return jsonify({'error': f'网络请求失败: {str(e)}'}), 500
    except Exception as e:
        logger.error(f'获取令牌时发生错误: {e}')
        return jsonify({'error': f'服务器内部错误: {str(e)}'}), 500

@app.route('/api/baidu/ocr', methods=['POST'])
def baidu_ocr_general():
    """百度通用文字识别的代理接口"""
    try:
        data = request.get_json()
        
        if not data or 'accessToken' not in data or 'image' not in data:
            return jsonify({'error': '缺少必要参数：accessToken 和 image'}), 400
        
        access_token = data['accessToken']
        image_data = data['image']
        options = data.get('options', {})
        
        logger.info('执行百度通用文字识别')
        
        # 准备请求数据
        post_data = {
            'image': image_data,
            'recognize_granularity': options.get('granularity', 'big')
        }
        
        if options.get('probability'):
            post_data['probability'] = 'true'
        if options.get('accuracy'):
            post_data['accuracy'] = 'high'
        
        # 调用百度OCR API
        response = requests.post(
            f'https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic?access_token={access_token}',
            data=post_data,
            timeout=30
        )
        
        result = response.json()
        
        if 'words_result' in result:
            logger.info(f'识别成功，识别到 {len(result["words_result"])} 行文字')
        else:
            logger.error(f'识别失败: {result}')
        
        return jsonify(result)
        
    except requests.exceptions.RequestException as e:
        logger.error(f'OCR请求失败: {e}')
        return jsonify({'error': f'OCR请求失败: {str(e)}'}), 500
    except Exception as e:
        logger.error(f'OCR识别时发生错误: {e}')
        return jsonify({'error': f'服务器内部错误: {str(e)}'}), 500

@app.route('/api/baidu/handwriting', methods=['POST'])
def baidu_ocr_handwriting():
    """百度手写文字识别的代理接口"""
    try:
        data = request.get_json()
        
        if not data or 'accessToken' not in data or 'image' not in data:
            return jsonify({'error': '缺少必要参数：accessToken 和 image'}), 400
        
        access_token = data['accessToken']
        image_data = data['image']
        options = data.get('options', {})
        
        logger.info('执行百度手写文字识别')
        
        # 准备请求数据
        post_data = {
            'image': image_data,
            'recognize_granularity': options.get('granularity', 'big')
        }
        
        # 调用百度手写文字识别API
        response = requests.post(
            f'https://aip.baidubce.com/rest/2.0/ocr/v1/handwriting?access_token={access_token}',
            data=post_data,
            timeout=30
        )
        
        result = response.json()
        
        if 'words_result' in result:
            logger.info(f'手写识别成功，识别到 {len(result["words_result"])} 行文字')
        else:
            logger.error(f'手写识别失败: {result}')
        
        return jsonify(result)
        
    except requests.exceptions.RequestException as e:
        logger.error(f'手写识别请求失败: {e}')
        return jsonify({'error': f'手写识别请求失败: {str(e)}'}), 500
    except Exception as e:
        logger.error(f'手写识别时发生错误: {e}')
        return jsonify({'error': f'服务器内部错误: {str(e)}'}), 500

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({'error': '接口不存在'}), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return jsonify({'error': '服务器内部错误'}), 500

if __name__ == '__main__':
    print(f"""
百度OCR代理服务器启动中...
端口: {PORT}
调试模式: {DEBUG}

可用接口:
  GET  /health                    - 健康检查
  POST /api/baidu/token          - 获取访问令牌
  POST /api/baidu/ocr            - 通用文字识别
  POST /api/baidu/handwriting    - 手写文字识别

访问地址: http://localhost:{PORT}
健康检查: http://localhost:{PORT}/health
""")
    
    try:
        app.run(
            host='0.0.0.0',
            port=PORT,
            debug=DEBUG,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n代理服务器已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        print("请检查端口是否被占用，或尝试更改端口号")
