<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek简单测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold mb-6 text-center">DeepSeek API简单测试</h1>
        
        <div class="mb-4">
            <label class="block text-sm font-medium mb-2">API Key:</label>
            <input type="text" id="apiKey" placeholder="输入您的DeepSeek API Key" class="w-full px-3 py-2 border rounded-lg">
        </div>

        <div class="mb-4">
            <button id="testBtn" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg">
                测试DeepSeek API
            </button>
        </div>

        <div id="result" class="mt-4 p-4 bg-gray-50 rounded-lg min-h-32">
            <p class="text-gray-500">点击按钮开始测试...</p>
        </div>
    </div>

    <script>
        document.getElementById('testBtn').addEventListener('click', testDeepSeek);

        async function testDeepSeek() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const resultDiv = document.getElementById('result');

            if (!apiKey) {
                resultDiv.innerHTML = '<p class="text-red-600">请输入API Key</p>';
                return;
            }

            resultDiv.innerHTML = '<p class="text-blue-600">测试中...</p>';

            // 创建一个简单的测试图片
            const canvas = document.createElement('canvas');
            canvas.width = 200;
            canvas.height = 100;
            const ctx = canvas.getContext('2d');
            
            // 白色背景
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, 200, 100);
            
            // 黑色文字
            ctx.fillStyle = 'black';
            ctx.font = '16px Arial';
            ctx.fillText('Hello World', 20, 30);
            ctx.fillText('你好世界', 20, 60);
            
            const imageBase64 = canvas.toDataURL();

            // 测试不同的API格式
            const formats = [
                {
                    name: '格式1: DeepSeek原生格式',
                    body: {
                        model: 'deepseek-vl-chat',
                        messages: [
                            {
                                role: 'user',
                                content: '请识别图片中的文字',
                                images: [imageBase64]
                            }
                        ],
                        max_tokens: 100
                    }
                },
                {
                    name: '格式2: OpenAI兼容格式',
                    body: {
                        model: 'deepseek-vl-chat',
                        messages: [
                            {
                                role: 'user',
                                content: [
                                    {
                                        type: 'text',
                                        text: '请识别图片中的文字'
                                    },
                                    {
                                        type: 'image_url',
                                        image_url: {
                                            url: imageBase64
                                        }
                                    }
                                ]
                            }
                        ],
                        max_tokens: 100
                    }
                },
                {
                    name: '格式3: 简化格式',
                    body: {
                        model: 'deepseek-vl-chat',
                        messages: [
                            {
                                role: 'user',
                                content: '请识别图片中的文字'
                            }
                        ],
                        images: [imageBase64],
                        max_tokens: 100
                    }
                }
            ];

            let results = [];

            for (const format of formats) {
                try {
                    console.log(`测试 ${format.name}...`);
                    
                    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${apiKey}`,
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(format.body)
                    });

                    if (response.ok) {
                        const data = await response.json();
                        results.push({
                            format: format.name,
                            success: true,
                            response: data.choices[0].message.content
                        });
                        break; // 成功了就不用测试其他格式
                    } else {
                        const errorText = await response.text();
                        let errorData;
                        try {
                            errorData = JSON.parse(errorText);
                        } catch (e) {
                            errorData = { error: { message: errorText } };
                        }
                        
                        results.push({
                            format: format.name,
                            success: false,
                            status: response.status,
                            error: errorData.error?.message || errorText
                        });
                    }
                } catch (error) {
                    results.push({
                        format: format.name,
                        success: false,
                        error: error.message
                    });
                }
            }

            // 显示结果
            let html = '';
            
            const successResult = results.find(r => r.success);
            if (successResult) {
                html += `
                    <div class="mb-4 p-3 bg-green-50 border border-green-200 rounded">
                        <h3 class="font-bold text-green-800">✅ 测试成功！</h3>
                        <p><strong>成功格式:</strong> ${successResult.format}</p>
                        <p><strong>AI回复:</strong> ${successResult.response}</p>
                    </div>
                `;
            } else {
                html += `
                    <div class="mb-4 p-3 bg-red-50 border border-red-200 rounded">
                        <h3 class="font-bold text-red-800">❌ 所有格式都失败了</h3>
                    </div>
                `;
            }

            html += '<h4 class="font-bold mb-2">详细测试结果:</h4>';
            
            for (const result of results) {
                const bgColor = result.success ? 'bg-green-50' : 'bg-red-50';
                const textColor = result.success ? 'text-green-800' : 'text-red-800';
                const icon = result.success ? '✅' : '❌';
                
                html += `
                    <div class="mb-2 p-2 ${bgColor} rounded">
                        <p class="${textColor}">
                            ${icon} <strong>${result.format}</strong>
                        </p>
                        ${result.success ? 
                            `<p class="text-sm">回复: ${result.response}</p>` :
                            `<p class="text-sm">错误: ${result.error}</p>`
                        }
                        ${result.status ? `<p class="text-xs">状态码: ${result.status}</p>` : ''}
                    </div>
                `;
            }

            // 添加建议
            if (!successResult) {
                html += `
                    <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
                        <h4 class="font-bold text-yellow-800">💡 建议:</h4>
                        <ul class="text-sm text-yellow-700 list-disc list-inside mt-2">
                            <li>检查API Key是否正确</li>
                            <li>确认账户已开通视觉模型权限</li>
                            <li>检查账户余额是否充足</li>
                            <li>尝试联系DeepSeek技术支持</li>
                        </ul>
                    </div>
                `;
            } else {
                html += `
                    <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
                        <h4 class="font-bold text-blue-800">🎉 成功！</h4>
                        <p class="text-sm text-blue-700 mt-2">
                            您的DeepSeek API可以正常使用视觉识别功能。
                            现在可以在主应用中使用DeepSeek进行OCR识别了。
                        </p>
                    </div>
                `;
            }

            resultDiv.innerHTML = html;
        }
    </script>
</body>
</html>
