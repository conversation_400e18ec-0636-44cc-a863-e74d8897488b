<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek API测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-3xl font-bold mb-6 text-center text-purple-700">DeepSeek API测试工具</h1>
        
        <!-- API配置 -->
        <div class="mb-6 p-4 bg-purple-50 rounded-lg">
            <h2 class="text-xl font-semibold mb-3">API配置</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-1">API Key:</label>
                    <input type="text" id="apiKey" placeholder="输入您的DeepSeek API Key" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-1">Base URL:</label>
                    <input type="text" id="baseUrl" value="https://api.deepseek.com/v1" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                </div>
            </div>
            <div class="mt-3 text-sm text-gray-600">
                💡 如果没有API Key，可以访问 <a href="https://platform.deepseek.com/" target="_blank" class="text-purple-600 hover:underline">DeepSeek平台</a> 获取
            </div>
        </div>

        <!-- 测试选项 -->
        <div class="mb-6">
            <h2 class="text-xl font-semibold mb-3">测试选项</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button id="testConnection" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                    测试连接
                </button>
                <button id="testTextOnly" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                    测试文本对话
                </button>
                <button id="testVision" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg">
                    测试视觉识别
                </button>
            </div>
        </div>

        <!-- 图片上传（用于视觉测试） -->
        <div class="mb-6" id="imageSection" style="display: none;">
            <h2 class="text-xl font-semibold mb-3">图片上传</h2>
            <input type="file" id="imageInput" accept="image/*" class="mb-3">
            <div id="imagePreview" class="hidden">
                <img id="previewImg" class="max-w-full h-auto border rounded max-h-64">
            </div>
            <button id="generateTestImage" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                生成测试图片
            </button>
        </div>

        <!-- 测试画布 -->
        <div class="mb-6 hidden" id="canvasSection">
            <canvas id="testCanvas" width="400" height="200" class="border rounded"></canvas>
        </div>

        <!-- 结果显示 -->
        <div id="results" class="mb-6">
            <h2 class="text-xl font-semibold mb-3">测试结果</h2>
            <div id="resultContent" class="bg-gray-50 p-4 rounded-lg min-h-32">
                <p class="text-gray-500">点击上方按钮开始测试...</p>
            </div>
        </div>

        <!-- 错误诊断 -->
        <div id="diagnostics" class="hidden">
            <h2 class="text-xl font-semibold mb-3 text-red-600">错误诊断</h2>
            <div id="diagnosticContent" class="bg-red-50 p-4 rounded-lg border border-red-200">
            </div>
        </div>
    </div>

    <script>
        // 事件监听
        document.getElementById('testConnection').addEventListener('click', testConnection);
        document.getElementById('testTextOnly').addEventListener('click', testTextOnly);
        document.getElementById('testVision').addEventListener('click', testVision);
        document.getElementById('generateTestImage').addEventListener('click', generateTestImage);
        document.getElementById('imageInput').addEventListener('change', handleImageUpload);

        function showResult(content, isError = false) {
            const resultDiv = document.getElementById('resultContent');
            const diagnosticsDiv = document.getElementById('diagnostics');
            
            if (isError) {
                resultDiv.innerHTML = `<p class="text-red-600">测试失败，请查看下方错误诊断</p>`;
                document.getElementById('diagnosticContent').innerHTML = content;
                diagnosticsDiv.classList.remove('hidden');
            } else {
                resultDiv.innerHTML = content;
                diagnosticsDiv.classList.add('hidden');
            }
        }

        function showLoading(message) {
            const resultDiv = document.getElementById('resultContent');
            resultDiv.innerHTML = `
                <div class="flex items-center">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600 mr-3"></div>
                    <span>${message}</span>
                </div>
            `;
        }

        async function testConnection() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const baseUrl = document.getElementById('baseUrl').value.trim();

            if (!apiKey) {
                showResult('请输入API Key', true);
                return;
            }

            showLoading('测试连接中...');

            try {
                const response = await fetch(`${baseUrl}/models`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    showResult(`
                        <div class="text-green-600">
                            <h3 class="font-bold mb-2">✅ 连接成功！</h3>
                            <p>状态码: ${response.status}</p>
                            <p>可用模型数量: ${data.data ? data.data.length : '未知'}</p>
                            <details class="mt-2">
                                <summary class="cursor-pointer">查看详细响应</summary>
                                <pre class="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">${JSON.stringify(data, null, 2)}</pre>
                            </details>
                        </div>
                    `);
                } else {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
            } catch (error) {
                showResult(`
                    <h3 class="font-bold mb-2">连接失败</h3>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                    <p><strong>可能原因:</strong></p>
                    <ul class="list-disc list-inside mt-2">
                        <li>API Key无效或过期</li>
                        <li>Base URL不正确</li>
                        <li>网络连接问题</li>
                        <li>CORS跨域限制</li>
                    </ul>
                    <p class="mt-2"><strong>建议:</strong></p>
                    <ul class="list-disc list-inside">
                        <li>检查API Key是否正确</li>
                        <li>确认账户余额充足</li>
                        <li>尝试不同的Base URL</li>
                    </ul>
                `, true);
            }
        }

        async function testTextOnly() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const baseUrl = document.getElementById('baseUrl').value.trim();

            if (!apiKey) {
                showResult('请输入API Key', true);
                return;
            }

            showLoading('测试文本对话中...');

            try {
                const response = await fetch(`${baseUrl}/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`,
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'deepseek-chat',
                        messages: [
                            {
                                role: 'user',
                                content: '你好，请回复"测试成功"'
                            }
                        ],
                        max_tokens: 100,
                        temperature: 0.1
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const reply = data.choices[0].message.content;
                    showResult(`
                        <div class="text-green-600">
                            <h3 class="font-bold mb-2">✅ 文本对话测试成功！</h3>
                            <p><strong>AI回复:</strong> ${reply}</p>
                            <p><strong>使用模型:</strong> ${data.model || 'deepseek-chat'}</p>
                            <p><strong>消耗Token:</strong> ${data.usage ? data.usage.total_tokens : '未知'}</p>
                        </div>
                    `);
                } else {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
            } catch (error) {
                showResult(`
                    <h3 class="font-bold mb-2">文本对话测试失败</h3>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                    <p class="mt-2"><strong>可能原因:</strong></p>
                    <ul class="list-disc list-inside">
                        <li>模型名称不正确</li>
                        <li>请求格式有误</li>
                        <li>账户权限不足</li>
                    </ul>
                `, true);
            }
        }

        async function testVision() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const baseUrl = document.getElementById('baseUrl').value.trim();

            if (!apiKey) {
                showResult('请输入API Key', true);
                return;
            }

            // 显示图片上传区域
            document.getElementById('imageSection').style.display = 'block';

            const img = document.getElementById('previewImg');
            if (!img.src) {
                showResult('请先上传图片或生成测试图片', true);
                return;
            }

            showLoading('测试视觉识别中...');

            try {
                // 获取图片的base64
                const imageBase64 = await getImageAsBase64(img);

                const response = await fetch(`${baseUrl}/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`,
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        model: 'deepseek-vl-chat',
                        messages: [
                            {
                                role: 'user',
                                content: [
                                    {
                                        type: 'text',
                                        text: '请描述这张图片中的内容'
                                    },
                                    {
                                        type: 'image_url',
                                        image_url: {
                                            url: imageBase64
                                        }
                                    }
                                ]
                            }
                        ],
                        max_tokens: 500,
                        temperature: 0.1
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const description = data.choices[0].message.content;
                    showResult(`
                        <div class="text-green-600">
                            <h3 class="font-bold mb-2">✅ 视觉识别测试成功！</h3>
                            <p><strong>AI描述:</strong></p>
                            <div class="bg-gray-100 p-3 rounded mt-2">${description}</div>
                            <p class="mt-2"><strong>使用模型:</strong> ${data.model || 'deepseek-vl-chat'}</p>
                            <p><strong>消耗Token:</strong> ${data.usage ? data.usage.total_tokens : '未知'}</p>
                        </div>
                    `);
                } else {
                    const errorText = await response.text();
                    let errorData;
                    try {
                        errorData = JSON.parse(errorText);
                    } catch (e) {
                        errorData = { error: { message: errorText } };
                    }
                    
                    throw new Error(`HTTP ${response.status}: ${errorData.error?.message || errorText}`);
                }
            } catch (error) {
                showResult(`
                    <h3 class="font-bold mb-2">视觉识别测试失败</h3>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                    <p class="mt-2"><strong>可能原因:</strong></p>
                    <ul class="list-disc list-inside">
                        <li>模型 'deepseek-vl-chat' 不可用</li>
                        <li>图片格式不支持</li>
                        <li>图片过大</li>
                        <li>请求格式错误</li>
                    </ul>
                    <p class="mt-2"><strong>建议:</strong></p>
                    <ul class="list-disc list-inside">
                        <li>确认账户已开通视觉模型权限</li>
                        <li>尝试更小的图片</li>
                        <li>检查图片格式（建议JPG/PNG）</li>
                    </ul>
                `, true);
            }
        }

        function generateTestImage() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 设置字体和样式
            ctx.fillStyle = 'black';
            ctx.font = '20px Arial';
            
            // 绘制测试文字
            ctx.fillText('苹果 apple', 20, 40);
            ctx.fillText('香蕉 banana', 20, 80);
            ctx.fillText('橙子 orange', 20, 120);
            ctx.fillText('葡萄 grape', 20, 160);
            
            // 显示画布
            document.getElementById('canvasSection').classList.remove('hidden');
            
            // 转换为图片
            canvas.toBlob(blob => {
                const url = URL.createObjectURL(blob);
                const img = document.getElementById('previewImg');
                img.src = url;
                document.getElementById('imagePreview').classList.remove('hidden');
            });
        }

        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const img = document.getElementById('previewImg');
                img.src = e.target.result;
                document.getElementById('imagePreview').classList.remove('hidden');
            };
            reader.readAsDataURL(file);
        }

        function getImageAsBase64(img) {
            return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = img.naturalWidth;
                canvas.height = img.naturalHeight;
                ctx.drawImage(img, 0, 0);
                resolve(canvas.toDataURL());
            });
        }
    </script>
</body>
</html>
