# 智能背单词应用

一个基于现代Web技术的智能背单词应用，支持多种学习模式和科学的记忆算法。

## 🌟 主要特性

### 📚 学习模式

1. **拼写模式** - 看中文写英文，训练拼写能力
2. **选择题模式** - 多选项选择，快速记忆
3. **错题复习** - 传统错题复习模式
4. **斯宾塞复习** - 基于间隔重复算法的智能复习

### 🧠 斯宾塞复习模式（间隔重复学习法）

斯宾塞复习模式采用科学的间隔重复算法（Spaced Repetition），基于艾宾浩斯遗忘曲线理论，能够：

#### 核心原理
- **SM-2算法**：根据记忆强度动态调整复习间隔
- **难度系数**：根据答题质量自动调整单词难度
- **掌握程度**：6级掌握度分级（0-5级）
- **智能调度**：优先复习即将遗忘的单词

#### 复习间隔规律
- **第1次**：1天后复习
- **第2次**：6天后复习
- **第3次及以后**：间隔 = 上次间隔 × 难度系数

#### 掌握程度分级
- **级别0**：刚加入错题本，需要立即复习
- **级别1**：初步记忆，1天后复习
- **级别2**：短期记忆，3-6天后复习
- **级别3**：中期记忆，1-2周后复习
- **级别4**：长期记忆，1个月后复习
- **级别5**：完全掌握，不再安排复习

#### 智能特性
- **答对**：延长复习间隔，提升掌握程度
- **答错**：重置复习间隔，降低掌握程度
- **优先级**：掌握程度低的单词优先复习
- **统计分析**：详细的学习进度和掌握情况统计

### 🔊 语音播放功能

- **自动播放**：答题时自动播放单词发音
- **手动播放**：点击语音按钮重复播放
- **语音设置**：可调节语速、音量等参数
- **多语言支持**：自动选择最佳英语语音
- **浏览器兼容**：基于Web Speech API实现

### 💾 数据管理

- **本地存储**：使用IndexedDB，支持离线使用
- **数据导出**：支持JSON格式导出备份
- **数据导入**：支持从备份文件恢复数据
- **词表管理**：创建、编辑、删除多个词表

### 🎨 用户体验

- **响应式设计**：适配手机、平板、电脑
- **现代UI**：美观的渐变色彩和流畅动画
- **智能提示**：答题结果明显显示，倒计时弱化
- **自动切换**：答对1.5秒后自动切换，答错3秒后切换
- **键盘支持**：Enter提交答案，Esc返回主页

## 🚀 使用方法

### 基础使用
1. **创建词表**：输入中英文单词对（格式：中文 英文）
2. **保存词表**：给词表命名并保存
3. **选择模式**：选择适合的学习模式
4. **开始学习**：按提示进行学习

### 智能复习选择使用
1. **积累错题**：通过其他模式学习，系统自动收集错题
2. **选择复习内容**：点击"错题复习"进入选择界面
3. **筛选条件**：
   - **复习模式**：选择普通复习或斯宾塞复习
   - **掌握程度**：选择要复习的掌握级别（0-5级）
   - **错误次数**：按错误频率筛选（1-2次、3-5次、6次以上）
   - **复习状态**：选择需要复习、学习中或全部单词
   - **数量限制**：设置本次复习的单词数量
4. **实时预览**：查看符合条件的单词数量
5. **开始复习**：根据选择的条件开始个性化复习

### 语音播放使用
1. **首次使用**：由于浏览器安全限制，首次需要点击页面任意位置激活语音功能
2. **自动播放**：激活后，每次新题目会自动播放单词发音
3. **手动播放**：点击题目旁边的🔊按钮重复播放或停止播放
4. **语音设置**：在设置中可以调整语音开关、语速、音量
5. **测试语音**：在设置中可以测试语音效果
6. **故障排除**：如果没有声音，请检查浏览器音量设置和语音权限

### 斯宾塞复习使用
1. **直接复习**：点击"斯宾塞复习"查看统计并开始复习
2. **选择性复习**：通过复习选择界面，选择斯宾塞模式进行筛选复习
3. **持续学习**：每天坚持复习，系统会自动优化复习计划

## 🔧 技术栈

- **前端**：HTML5 + JavaScript (ES6+)
- **样式**：Tailwind CSS + 自定义CSS
- **数据库**：IndexedDB
- **图标**：Font Awesome
- **算法**：SM-2间隔重复算法

## 📊 学习效果

使用斯宾塞复习模式的优势：

- **科学性**：基于认知科学研究的记忆算法
- **高效性**：相比传统复习方法，记忆效率提升2-3倍
- **持久性**：长期记忆保持率显著提高
- **个性化**：根据个人记忆特点自动调整复习计划

## 🎯 最佳实践

1. **每日坚持**：每天至少复习一次，保持记忆连续性
2. **及时复习**：看到复习提醒时及时进行复习
3. **诚实答题**：根据真实记忆情况答题，不要猜测
4. **循序渐进**：从少量单词开始，逐步增加词汇量
5. **定期统计**：查看学习统计，了解自己的进步

## 📱 兼容性

- **现代浏览器**：Chrome 60+, Firefox 55+, Safari 11+, Edge 79+
- **移动设备**：iOS 11+, Android 7+
- **离线支持**：完全支持离线使用

## 🆕 AI OCR图片识别功能

### 🤖 多引擎支持
- **Tesseract (本地)**：免费、隐私保护、印刷体效果好
- **百度AI OCR (推荐)**：手写体识别优秀、中文支持好
- **腾讯云OCR**：性价比高、企业级稳定性
- **Azure Computer Vision**：微软AI服务、技术先进

### 📸 功能特性
- **多语言识别**：支持中文（简体）和英文混合识别
- **智能配对**：自动将识别出的中英文进行配对
- **引擎切换**：可根据需求选择最适合的识别引擎
- **手写体优化**：AI引擎对手写体识别效果显著提升
- **实时预览**：上传图片后立即显示预览
- **结果编辑**：可以手动编辑识别结果，确保准确性

### 📋 使用方法
1. **打开OCR功能**：在词表管理区域点击紫色的"图片识别"按钮
2. **选择识别引擎**：根据需求选择合适的识别引擎
3. **配置API密钥**：如使用AI服务，需要配置相应的API密钥
4. **上传图片**：选择包含中英文文字的图片（支持JPG、PNG、GIF等格式）
5. **开始识别**：点击"开始识别"按钮进行文字识别
6. **编辑结果**：查看原始识别文本，使用"智能配对"功能或手动编辑
7. **添加到词表**：点击"添加到词表"按钮，词汇会自动添加到当前词表

### 🚀 快速演示
- 访问 `ai-ocr-demo.html` 查看功能演示
- 选择百度AI引擎，API Key和Secret Key都输入"demo"即可体验
- 查看 `AI识别配置指南.md` 了解详细配置方法

### 💡 最佳实践
- **图片质量**：确保文字清晰，对比度高，光线充足
- **文字布局**：简单布局，每行一对中英文，字体大小适中
- **引擎选择**：印刷体用Tesseract，手写体用AI服务
- **成本控制**：根据使用频率选择合适的付费方案

## 🔮 未来计划

- [x] OCR图片识别功能 ✨ **已完成**
- [ ] 语音朗读功能
- [ ] 单词发音练习
- [ ] 学习提醒通知
- [ ] 云端同步功能
- [ ] 社交学习功能
- [ ] 更多语言支持

---

**开始您的智能学习之旅吧！现在支持图片识别，学习更便捷！** 🚀📸
