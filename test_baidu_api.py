#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
百度API密钥测试脚本
用于验证API密钥是否有效
"""

import requests
import json
import base64
import time

# 您的API密钥
API_KEY = "29GK89PIpJMUZU7G1oOOvhZG"
SECRET_KEY = "XwBtrcoU1yOFbhqGIN0BuewqtfGI6RHT"

def get_access_token():
    """获取访问令牌"""
    print("正在获取访问令牌...")
    
    url = "https://aip.baidubce.com/oauth/2.0/token"
    params = {
        "grant_type": "client_credentials",
        "client_id": API_KEY,
        "client_secret": SECRET_KEY
    }
    
    try:
        response = requests.post(url, params=params, timeout=10)
        result = response.json()
        
        if "access_token" in result:
            print(f"✅ 成功获取访问令牌")
            print(f"   令牌: {result['access_token'][:20]}...")
            print(f"   有效期: {result['expires_in']} 秒")
            return result["access_token"]
        else:
            print(f"❌ 获取令牌失败: {result}")
            return None
            
    except Exception as e:
        print(f"❌ 网络请求失败: {e}")
        return None

def test_ocr_with_sample_image(access_token):
    """使用示例图片测试OCR"""
    print("\n正在测试OCR功能...")
    
    # 创建一个简单的测试图片（base64编码）
    # 这是一个包含"Hello 世界"文字的简单图片
    sample_image_base64 = """
    iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==
    """
    
    # 实际使用时，您可以这样编码图片：
    # with open("test_image.jpg", "rb") as f:
    #     image_data = base64.b64encode(f.read()).decode()
    
    url = f"https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic?access_token={access_token}"
    
    data = {
        "image": sample_image_base64.strip(),
        "recognize_granularity": "big"
    }
    
    try:
        response = requests.post(url, data=data, timeout=30)
        result = response.json()
        
        if "words_result" in result:
            print(f"✅ OCR测试成功")
            print(f"   识别到 {len(result['words_result'])} 行文字")
            for i, word in enumerate(result['words_result']):
                print(f"   第{i+1}行: {word['words']}")
        else:
            print(f"⚠️  OCR测试返回: {result}")
            if result.get('error_code'):
                error_messages = {
                    4: "请求参数缺失",
                    17: "每天请求量超限额",
                    18: "QPS超限额", 
                    19: "请求总量超限额",
                    216015: "模块关闭",
                    216100: "无效参数",
                    216101: "参数缺失",
                    216102: "服务不可用",
                    216103: "请求中包含非法等字符",
                    216110: "appid不存在",
                    282000: "服务器内部错误"
                }
                error_code = result.get('error_code')
                error_msg = error_messages.get(error_code, "未知错误")
                print(f"   错误码: {error_code}")
                print(f"   错误信息: {error_msg}")
                
    except Exception as e:
        print(f"❌ OCR请求失败: {e}")

def test_proxy_server():
    """测试代理服务器"""
    print("\n正在测试代理服务器...")
    
    try:
        # 测试健康检查
        response = requests.get("http://localhost:3001/health", timeout=5)
        if response.status_code == 200:
            print("✅ 代理服务器运行正常")
            
            # 测试获取令牌
            token_response = requests.post(
                "http://localhost:3001/api/baidu/token",
                json={"apiKey": API_KEY, "secretKey": SECRET_KEY},
                timeout=10
            )
            
            if token_response.status_code == 200:
                result = token_response.json()
                if "access_token" in result:
                    print("✅ 通过代理服务器成功获取令牌")
                    return True
                else:
                    print(f"❌ 代理服务器返回错误: {result}")
            else:
                print(f"❌ 代理服务器请求失败: {token_response.status_code}")
                
        else:
            print(f"❌ 代理服务器响应异常: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到代理服务器")
        print("   请确保代理服务器已启动: python baidu_proxy.py")
    except Exception as e:
        print(f"❌ 代理服务器测试失败: {e}")
    
    return False

def main():
    """主函数"""
    print("=" * 50)
    print("百度OCR API密钥测试")
    print("=" * 50)
    print(f"API Key: {API_KEY}")
    print(f"Secret Key: {SECRET_KEY[:8]}...")
    print("=" * 50)
    
    # 测试1: 直接获取访问令牌
    access_token = get_access_token()
    
    if access_token:
        # 测试2: OCR功能测试
        test_ocr_with_sample_image(access_token)
    
    # 测试3: 代理服务器测试
    proxy_ok = test_proxy_server()
    
    print("\n" + "=" * 50)
    print("测试总结:")
    print("=" * 50)
    
    if access_token:
        print("✅ API密钥有效，可以正常使用")
    else:
        print("❌ API密钥无效，请检查密钥是否正确")
        
    if proxy_ok:
        print("✅ 代理服务器正常，可以在应用中使用")
    else:
        print("❌ 代理服务器异常，请启动代理服务器")
        
    print("\n使用建议:")
    if access_token and proxy_ok:
        print("🎉 一切正常！您可以在应用中使用百度AI识别功能")
        print("   1. 确保代理服务器保持运行")
        print("   2. 在应用中选择'百度AI'引擎")
        print("   3. 输入您的API密钥")
        print("   4. 开始享受高精度识别！")
    elif access_token:
        print("⚠️  API密钥有效，但需要启动代理服务器")
        print("   运行: python baidu_proxy.py")
    else:
        print("❌ 请检查API密钥或网络连接")

if __name__ == "__main__":
    main()
