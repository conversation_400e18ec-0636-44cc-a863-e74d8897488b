/**
 * OCR图片识别模块
 * 使用Tesseract.js进行客户端文字识别
 */

class OCRManager {
    constructor() {
        this.worker = null;
        this.isInitialized = false;
        this.currentImage = null;
        this.initializeEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // OCR按钮点击
        document.getElementById('ocrBtn').addEventListener('click', () => this.showOCRSection());

        // 图片文件选择
        document.getElementById('ocrImageFile').addEventListener('change', (e) => this.handleImageUpload(e));

        // 清除图片
        document.getElementById('clearImageBtn').addEventListener('click', () => this.clearImage());

        // 开始识别
        document.getElementById('startOcrBtn').addEventListener('click', () => this.startOCR());

        // 智能配对
        document.getElementById('autoMatchBtn').addEventListener('click', () => this.autoMatchWords());

        // 添加到词表
        document.getElementById('addOcrResultsBtn').addEventListener('click', () => this.addResultsToWordlist());

        // 识别引擎选择
        document.querySelectorAll('input[name="ocrEngine"]').forEach(radio => {
            radio.addEventListener('change', (e) => this.handleEngineChange(e.target.value));
        });

        // API配置输入
        this.initApiConfigListeners();
    }

    /**
     * 显示OCR功能区域
     */
    showOCRSection() {
        const ocrSection = document.getElementById('ocrSection');
        ocrSection.classList.remove('hidden');
        
        // 触发文件选择
        document.getElementById('ocrImageFile').click();
    }

    /**
     * 处理图片上传
     */
    async handleImageUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        // 验证文件类型
        if (!file.type.startsWith('image/')) {
            this.showMessage('请选择有效的图片文件', 'error');
            return;
        }

        // 验证文件大小 (限制为10MB)
        if (file.size > 10 * 1024 * 1024) {
            this.showMessage('图片文件过大，请选择小于10MB的图片', 'error');
            return;
        }

        try {
            // 显示图片预览
            await this.showImagePreview(file);
            this.currentImage = file;
            
            // 启用识别按钮
            document.getElementById('startOcrBtn').disabled = false;
            
            this.showMessage('图片上传成功，点击"开始识别"进行文字识别', 'success');
        } catch (error) {
            console.error('图片处理失败:', error);
            this.showMessage('图片处理失败，请重试', 'error');
        }
    }

    /**
     * 显示图片预览
     */
    showImagePreview(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const previewImage = document.getElementById('previewImage');
                previewImage.src = e.target.result;
                
                document.getElementById('imagePreview').classList.remove('hidden');
                resolve();
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    /**
     * 清除图片
     */
    clearImage() {
        this.currentImage = null;
        document.getElementById('imagePreview').classList.add('hidden');
        document.getElementById('ocrResults').classList.add('hidden');
        document.getElementById('startOcrBtn').disabled = true;
        document.getElementById('ocrImageFile').value = '';
        
        // 清空结果
        document.getElementById('rawOcrText').value = '';
        document.getElementById('pairedWords').value = '';
    }

    /**
     * 初始化OCR Worker
     */
    async initializeOCR() {
        if (this.isInitialized) return;

        try {
            this.worker = await Tesseract.createWorker({
                logger: m => {
                    if (m.status === 'recognizing text') {
                        const progress = Math.round(m.progress * 100);
                        document.getElementById('ocrProgressText').textContent = `识别中... ${progress}%`;
                    }
                }
            });

            // 加载中英文语言包
            await this.worker.loadLanguage('eng+chi_sim');
            await this.worker.initialize('eng+chi_sim');

            this.isInitialized = true;
            console.log('OCR引擎初始化成功');
        } catch (error) {
            console.error('OCR引擎初始化失败:', error);
            throw new Error('OCR引擎初始化失败');
        }
    }

    /**
     * 开始OCR识别
     */
    async startOCR() {
        if (!this.currentImage) {
            this.showMessage('请先上传图片', 'error');
            return;
        }

        try {
            // 显示进度
            this.showProgress(true);
            document.getElementById('startOcrBtn').disabled = true;

            // 获取选择的识别引擎
            const selectedEngine = document.querySelector('input[name="ocrEngine"]:checked').value;

            let recognizedText = '';

            if (selectedEngine === 'tesseract') {
                // 使用Tesseract本地识别
                recognizedText = await this.recognizeWithTesseract();
            } else {
                // 使用AI识别服务
                recognizedText = await this.recognizeWithAI(selectedEngine);
            }

            // 显示原始结果
            document.getElementById('rawOcrText').value = recognizedText;

            // 自动配对词汇
            this.autoMatchWords(recognizedText);

            // 显示结果区域
            document.getElementById('ocrResults').classList.remove('hidden');

            this.showMessage('文字识别完成！', 'success');
        } catch (error) {
            console.error('OCR识别失败:', error);
            this.showMessage(`文字识别失败: ${error.message}`, 'error');
        } finally {
            this.showProgress(false);
            document.getElementById('startOcrBtn').disabled = false;
        }
    }

    /**
     * 智能配对中英文词汇
     */
    autoMatchWords(text = null) {
        const rawText = text || document.getElementById('rawOcrText').value;
        if (!rawText.trim()) {
            this.showMessage('没有识别到文字内容', 'error');
            return;
        }

        try {
            const pairedWords = this.parseAndMatchWords(rawText);
            document.getElementById('pairedWords').value = pairedWords;
            
            if (pairedWords.trim()) {
                this.showMessage('智能配对完成！请检查并编辑配对结果', 'success');
            } else {
                this.showMessage('未能自动配对词汇，请手动编辑', 'warning');
            }
        } catch (error) {
            console.error('智能配对失败:', error);
            this.showMessage('智能配对失败，请手动编辑', 'error');
        }
    }

    /**
     * 解析和配对词汇
     */
    parseAndMatchWords(text) {
        // 清理文本
        const cleanText = text.replace(/[^\u4e00-\u9fa5a-zA-Z\s\n]/g, ' ')
                             .replace(/\s+/g, ' ')
                             .trim();

        const lines = cleanText.split('\n').map(line => line.trim()).filter(line => line);
        const pairedWords = [];

        for (const line of lines) {
            const words = line.split(/\s+/);
            const chineseWords = [];
            const englishWords = [];

            // 分离中英文
            for (const word of words) {
                if (/[\u4e00-\u9fa5]/.test(word)) {
                    chineseWords.push(word);
                } else if (/[a-zA-Z]/.test(word)) {
                    englishWords.push(word);
                }
            }

            // 尝试配对
            const maxPairs = Math.min(chineseWords.length, englishWords.length);
            for (let i = 0; i < maxPairs; i++) {
                pairedWords.push(`${chineseWords[i]} ${englishWords[i]}`);
            }

            // 处理剩余的单词
            if (chineseWords.length > englishWords.length) {
                for (let i = maxPairs; i < chineseWords.length; i++) {
                    pairedWords.push(`${chineseWords[i]} `);
                }
            } else if (englishWords.length > chineseWords.length) {
                for (let i = maxPairs; i < englishWords.length; i++) {
                    pairedWords.push(` ${englishWords[i]}`);
                }
            }
        }

        return pairedWords.join('\n');
    }

    /**
     * 添加识别结果到词表
     */
    async addResultsToWordlist() {
        const pairedText = document.getElementById('pairedWords').value.trim();
        if (!pairedText) {
            this.showMessage('没有可添加的词汇内容', 'error');
            return;
        }

        try {
            // 获取当前词表内容
            const currentContent = document.getElementById('wordlistContent').value.trim();
            
            // 合并内容
            const newContent = currentContent ? `${currentContent}\n${pairedText}` : pairedText;
            document.getElementById('wordlistContent').value = newContent;
            
            // 更新词表信息
            if (window.wordlistManager) {
                window.wordlistManager.updateWordCount();
            }
            
            this.showMessage('识别结果已添加到词表！', 'success');
            
            // 清理OCR结果
            this.clearOCRResults();
        } catch (error) {
            console.error('添加到词表失败:', error);
            this.showMessage('添加到词表失败，请重试', 'error');
        }
    }

    /**
     * 清理OCR结果
     */
    clearOCRResults() {
        document.getElementById('rawOcrText').value = '';
        document.getElementById('pairedWords').value = '';
        document.getElementById('ocrResults').classList.add('hidden');
    }

    /**
     * 显示/隐藏进度指示器
     */
    showProgress(show) {
        const progressElement = document.getElementById('ocrProgress');
        if (show) {
            progressElement.classList.remove('hidden');
        } else {
            progressElement.classList.add('hidden');
        }
    }

    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        // 使用现有的消息显示系统
        if (window.wordlistManager && window.wordlistManager.showMessage) {
            window.wordlistManager.showMessage(message, type);
        } else {
            // 备用消息显示
            console.log(`[${type.toUpperCase()}] ${message}`);
            alert(message);
        }
    }

    /**
     * 使用Tesseract进行本地识别
     */
    async recognizeWithTesseract() {
        // 初始化OCR引擎
        if (!this.isInitialized) {
            document.getElementById('ocrProgressText').textContent = '正在初始化OCR引擎...';
            await this.initializeOCR();
        }

        // 开始识别
        document.getElementById('ocrProgressText').textContent = '正在识别文字...';
        const { data: { text } } = await this.worker.recognize(this.currentImage);

        return text;
    }

    /**
     * 使用AI服务进行识别
     */
    async recognizeWithAI(engine) {
        // 验证API配置
        if (!window.aiOcrManager.validateConfig(engine)) {
            throw new Error(`请先配置${this.getEngineDisplayName(engine)}的API密钥`);
        }

        // 将图片转换为base64
        const imageBase64 = await this.imageToBase64(this.currentImage);

        // 调用AI识别服务
        document.getElementById('ocrProgressText').textContent = `正在使用${this.getEngineDisplayName(engine)}识别...`;
        const text = await window.aiOcrManager.recognize(engine, imageBase64);

        return text;
    }

    /**
     * 将图片文件转换为base64
     */
    imageToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    /**
     * 获取引擎显示名称
     */
    getEngineDisplayName(engine) {
        const names = {
            'baidu': '百度AI',
            'tencent': '腾讯云',
            'azure': 'Azure',
            'deepseek': 'DeepSeek Vision',
            'openai': 'GPT-4 Vision',
            'claude': 'Claude 3 Vision',
            'qwen': 'Qwen-VL'
        };
        return names[engine] || engine;
    }

    /**
     * 处理识别引擎变化
     */
    handleEngineChange(engine) {
        const apiConfig = document.getElementById('apiConfig');
        const configSections = document.querySelectorAll('.api-config-section');

        // 隐藏所有配置区域
        configSections.forEach(section => section.classList.add('hidden'));

        if (engine === 'tesseract') {
            // 隐藏API配置
            apiConfig.classList.add('hidden');
        } else {
            // 显示API配置
            apiConfig.classList.remove('hidden');

            // 显示对应的配置区域
            const targetConfig = document.getElementById(`${engine}Config`);
            if (targetConfig) {
                targetConfig.classList.remove('hidden');
            }

            // 加载保存的配置
            this.loadApiConfigToUI(engine);
        }

        // 更新引擎卡片样式
        this.updateEngineCardStyles();
    }

    /**
     * 初始化API配置监听器
     */
    initApiConfigListeners() {
        // 百度API配置
        document.getElementById('baiduApiKey').addEventListener('blur', () => {
            this.saveApiConfigFromUI('baidu');
        });
        document.getElementById('baiduSecretKey').addEventListener('blur', () => {
            this.saveApiConfigFromUI('baidu');
        });

        // 腾讯云API配置
        document.getElementById('tencentSecretId').addEventListener('blur', () => {
            this.saveApiConfigFromUI('tencent');
        });
        document.getElementById('tencentSecretKey').addEventListener('blur', () => {
            this.saveApiConfigFromUI('tencent');
        });

        // Azure API配置
        document.getElementById('azureApiKey').addEventListener('blur', () => {
            this.saveApiConfigFromUI('azure');
        });
        document.getElementById('azureEndpoint').addEventListener('blur', () => {
            this.saveApiConfigFromUI('azure');
        });

        // DeepSeek API配置
        document.getElementById('deepseekApiKey').addEventListener('blur', () => {
            this.saveLLMConfigFromUI('deepseek');
        });
        document.getElementById('deepseekBaseUrl').addEventListener('blur', () => {
            this.saveLLMConfigFromUI('deepseek');
        });

        // OpenAI API配置
        document.getElementById('openaiApiKey').addEventListener('blur', () => {
            this.saveLLMConfigFromUI('openai');
        });
        document.getElementById('openaiBaseUrl').addEventListener('blur', () => {
            this.saveLLMConfigFromUI('openai');
        });

        // Claude API配置
        document.getElementById('claudeApiKey').addEventListener('blur', () => {
            this.saveLLMConfigFromUI('claude');
        });
        document.getElementById('claudeBaseUrl').addEventListener('blur', () => {
            this.saveLLMConfigFromUI('claude');
        });

        // Qwen API配置
        document.getElementById('qwenApiKey').addEventListener('blur', () => {
            this.saveLLMConfigFromUI('qwen');
        });
        document.getElementById('qwenBaseUrl').addEventListener('blur', () => {
            this.saveLLMConfigFromUI('qwen');
        });
    }

    /**
     * 从UI保存API配置
     */
    saveApiConfigFromUI(engine) {
        const config = {};

        switch (engine) {
            case 'baidu':
                config.apiKey = document.getElementById('baiduApiKey').value.trim();
                config.secretKey = document.getElementById('baiduSecretKey').value.trim();
                break;
            case 'tencent':
                config.secretId = document.getElementById('tencentSecretId').value.trim();
                config.secretKey = document.getElementById('tencentSecretKey').value.trim();
                break;
            case 'azure':
                config.apiKey = document.getElementById('azureApiKey').value.trim();
                config.endpoint = document.getElementById('azureEndpoint').value.trim();
                break;
        }

        if (window.aiOcrManager) {
            window.aiOcrManager.updateApiConfig(engine, config);
        }
    }

    /**
     * 从UI保存LLM配置
     */
    saveLLMConfigFromUI(engine) {
        if (!window.llmOcrManager) return;

        const config = {};

        switch (engine) {
            case 'deepseek':
                config.apiKey = document.getElementById('deepseekApiKey').value.trim();
                config.baseUrl = document.getElementById('deepseekBaseUrl').value.trim();
                break;
            case 'openai':
                config.apiKey = document.getElementById('openaiApiKey').value.trim();
                config.baseUrl = document.getElementById('openaiBaseUrl').value.trim();
                break;
            case 'claude':
                config.apiKey = document.getElementById('claudeApiKey').value.trim();
                config.baseUrl = document.getElementById('claudeBaseUrl').value.trim();
                break;
            case 'qwen':
                config.apiKey = document.getElementById('qwenApiKey').value.trim();
                config.baseUrl = document.getElementById('qwenBaseUrl').value.trim();
                break;
        }

        window.llmOcrManager.updateApiConfig(engine, config);
    }

    /**
     * 加载API配置到UI
     */
    loadApiConfigToUI(engine) {
        // 检查是否是LLM引擎
        const llmEngines = ['deepseek', 'openai', 'claude', 'qwen'];
        if (llmEngines.includes(engine)) {
            this.loadLLMConfigToUI(engine);
            return;
        }

        // 传统OCR引擎配置
        if (!window.aiOcrManager) return;

        const config = window.aiOcrManager.apiConfigs[engine];
        if (!config) return;

        switch (engine) {
            case 'baidu':
                document.getElementById('baiduApiKey').value = config.apiKey || '';
                document.getElementById('baiduSecretKey').value = config.secretKey || '';
                break;
            case 'tencent':
                document.getElementById('tencentSecretId').value = config.secretId || '';
                document.getElementById('tencentSecretKey').value = config.secretKey || '';
                break;
            case 'azure':
                document.getElementById('azureApiKey').value = config.apiKey || '';
                document.getElementById('azureEndpoint').value = config.endpoint || '';
                break;
        }
    }

    /**
     * 加载LLM配置到UI
     */
    loadLLMConfigToUI(engine) {
        if (!window.llmOcrManager) return;

        const config = window.llmOcrManager.apiConfigs[engine];
        if (!config) return;

        switch (engine) {
            case 'deepseek':
                document.getElementById('deepseekApiKey').value = config.apiKey || '';
                document.getElementById('deepseekBaseUrl').value = config.baseUrl || 'https://api.deepseek.com';
                break;
            case 'openai':
                document.getElementById('openaiApiKey').value = config.apiKey || '';
                document.getElementById('openaiBaseUrl').value = config.baseUrl || 'https://api.openai.com/v1';
                break;
            case 'claude':
                document.getElementById('claudeApiKey').value = config.apiKey || '';
                document.getElementById('claudeBaseUrl').value = config.baseUrl || 'https://api.anthropic.com';
                break;
            case 'qwen':
                document.getElementById('qwenApiKey').value = config.apiKey || '';
                document.getElementById('qwenBaseUrl').value = config.baseUrl || 'https://dashscope.aliyuncs.com/api/v1';
                break;
        }
    }

    /**
     * 更新引擎卡片样式
     */
    updateEngineCardStyles() {
        const cards = document.querySelectorAll('.ocr-engine-card');
        cards.forEach(card => {
            const radio = card.querySelector('input[type="radio"]');
            if (radio.checked) {
                card.classList.add('border-purple-500', 'bg-purple-50');
                card.classList.remove('border-gray-200');
            } else {
                card.classList.remove('border-purple-500', 'bg-purple-50');
                card.classList.add('border-gray-200');
            }
        });
    }

    /**
     * 销毁OCR Worker
     */
    async destroy() {
        if (this.worker) {
            try {
                await this.worker.terminate();
            } catch (error) {
                console.error('销毁OCR Worker失败:', error);
            }
            this.worker = null;
            this.isInitialized = false;
        }
    }
}

// 创建全局OCR管理器实例
window.ocrManager = new OCRManager();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 初始化引擎卡片样式
    if (window.ocrManager) {
        window.ocrManager.updateEngineCardStyles();
    }
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.ocrManager) {
        window.ocrManager.destroy();
    }
});
