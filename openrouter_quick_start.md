# OpenRouter快速开始指南

## 🚨 解决字符编码问题

刚才的错误是因为HTTP头中包含了中文字符。我已经修复了这个问题。

## 🆓 OpenRouter免费方案

### 为什么选择OpenRouter？
- ✅ **Google Gemini Flash 1.5完全免费**
- ✅ **无需绑定信用卡**
- ✅ **注册即可使用**
- ✅ **标准OpenAI API格式**
- ✅ **稳定可靠的服务**

## 🚀 3分钟快速开始

### 步骤1: 获取免费API Key
1. 访问：https://openrouter.ai/
2. 点击"Sign Up"注册（使用邮箱即可）
3. 验证邮箱后登录
4. 进入Dashboard → Keys
5. 点击"Create Key"
6. 复制API Key（格式：sk-or-v1-...）

### 步骤2: 测试API
1. 打开测试页面：http://localhost:8000/openrouter_simple_test.html
2. 输入您的API Key
3. 点击"测试OpenRouter API"
4. 查看结果

### 步骤3: 在主应用中使用
1. 访问：http://localhost:8000/index.html
2. 点击"图片识别"
3. 选择"OpenRouter (免费)"
4. 输入API Key
5. 选择"Gemini Flash 1.5 (免费)"
6. 上传图片开始识别

## 🎯 演示模式

如果您想先体验效果：
1. 在任何OpenRouter配置中
2. API Key输入：`demo`
3. 立即体验模拟识别效果

## 💡 推荐配置

```
引擎: OpenRouter (免费)
API Key: sk-or-v1-your-actual-key
模型: google/gemini-flash-1.5
特点: 完全免费，识别效果优秀
```

## 🔧 故障排除

### 如果仍然遇到问题：

#### 1. 字符编码错误
- ✅ 已修复：移除了中文HTTP头
- ✅ 使用简化的请求格式

#### 2. CORS跨域问题
- 这是浏览器安全限制
- OpenRouter支持跨域请求
- 确保使用HTTPS

#### 3. API Key问题
- 确认格式：sk-or-v1-开头
- 检查是否完整复制
- 在OpenRouter控制台验证状态

#### 4. 网络问题
- 检查网络连接
- 尝试不同浏览器
- 稍后重试

## 🎉 成功标志

如果看到以下结果，说明成功了：
```
✅ OpenRouter测试成功！
使用模型: google/gemini-flash-1.5 (免费)
AI识别结果: Hello World 你好世界
🎉 现在可以在主应用中使用OpenRouter了！
```

## 🔄 替代方案

如果OpenRouter仍有问题，您还可以：

### 1. 使用其他免费/低成本LLM
- **Hugging Face** - 有免费的视觉模型
- **Replicate** - 按使用付费，价格低
- **Together AI** - 开源模型，价格便宜

### 2. 传统OCR方案
- **百度AI OCR** - 手写体效果好
- **Tesseract** - 完全免费，本地运行

### 3. 混合策略
- 优先使用免费LLM
- 失败时降级到传统OCR
- 根据图片类型智能选择

## 📞 获取帮助

### 如果需要进一步帮助：
1. **测试结果**：请提供简单测试页面的具体错误信息
2. **网络环境**：说明您的网络环境（公司/家庭/移动）
3. **浏览器信息**：使用的浏览器类型和版本
4. **API Key状态**：在OpenRouter控制台确认Key状态

## 🎯 下一步

一旦OpenRouter测试成功：
1. **在主应用中配置**
2. **上传真实的手写笔记或教科书图片**
3. **体验AI识别效果**
4. **享受免费的高质量OCR服务**

---

## 🚀 立即行动

1. **注册OpenRouter**：https://openrouter.ai/
2. **获取免费API Key**
3. **测试API**：http://localhost:8000/openrouter_simple_test.html
4. **开始使用**：在主应用中配置OpenRouter

**免费的AI视觉识别，就在您的指尖！** 🤖✨
