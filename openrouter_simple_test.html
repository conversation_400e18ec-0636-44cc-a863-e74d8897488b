<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenRouter简单测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold mb-6 text-center text-green-700">OpenRouter简单测试</h1>
        
        <div class="mb-4">
            <label class="block text-sm font-medium mb-2">OpenRouter API Key:</label>
            <input type="text" id="apiKey" placeholder="sk-or-v1-... 或输入 'demo' 体验" class="w-full px-3 py-2 border rounded-lg">
        </div>

        <div class="mb-4">
            <button id="testBtn" class="w-full bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg">
                测试OpenRouter API
            </button>
        </div>

        <div id="result" class="mt-4 p-4 bg-gray-50 rounded-lg min-h-32">
            <p class="text-gray-500">点击按钮开始测试...</p>
        </div>

        <div class="mt-4 text-sm text-gray-600">
            <p>💡 <strong>获取免费API Key:</strong></p>
            <ol class="list-decimal list-inside mt-2 space-y-1">
                <li>访问 <a href="https://openrouter.ai/" target="_blank" class="text-green-600 hover:underline">OpenRouter官网</a></li>
                <li>注册账号（完全免费）</li>
                <li>在Dashboard中创建API Key</li>
                <li>选择Gemini Flash 1.5免费模型</li>
            </ol>
        </div>
    </div>

    <script>
        document.getElementById('testBtn').addEventListener('click', testOpenRouter);

        async function testOpenRouter() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const resultDiv = document.getElementById('result');

            if (!apiKey) {
                resultDiv.innerHTML = '<p class="text-red-600">请输入OpenRouter API Key</p>';
                return;
            }

            // 演示模式
            if (apiKey === 'demo') {
                resultDiv.innerHTML = `
                    <div class="text-green-600">
                        <h3 class="font-bold mb-2">✅ 演示模式</h3>
                        <p>模拟OpenRouter免费视觉识别效果：</p>
                        <div class="bg-gray-100 p-3 rounded mt-2 font-mono text-sm">
苹果 apple<br>
香蕉 banana<br>
橙子 orange<br>
葡萄 grape<br>
草莓 strawberry
                        </div>
                        <p class="mt-2 text-sm">要使用真实API，请获取OpenRouter免费API Key。</p>
                    </div>
                `;
                return;
            }

            resultDiv.innerHTML = '<p class="text-blue-600">测试中...</p>';

            // 创建测试图片
            const canvas = document.createElement('canvas');
            canvas.width = 200;
            canvas.height = 100;
            const ctx = canvas.getContext('2d');
            
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, 200, 100);
            ctx.fillStyle = 'black';
            ctx.font = '16px Arial';
            ctx.fillText('Hello World', 20, 30);
            ctx.fillText('你好世界', 20, 60);
            
            const imageBase64 = canvas.toDataURL();

            try {
                // 简化的请求头，避免字符编码问题
                const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify({
                        model: 'google/gemini-flash-1.5',
                        messages: [
                            {
                                role: 'user',
                                content: [
                                    {
                                        type: 'text',
                                        text: 'Please identify all text in this image'
                                    },
                                    {
                                        type: 'image_url',
                                        image_url: {
                                            url: imageBase64
                                        }
                                    }
                                ]
                            }
                        ],
                        max_tokens: 100,
                        temperature: 0.1
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const content = data.choices[0].message.content;
                    
                    resultDiv.innerHTML = `
                        <div class="text-green-600">
                            <h3 class="font-bold mb-2">✅ OpenRouter测试成功！</h3>
                            <p><strong>使用模型:</strong> google/gemini-flash-1.5 (免费)</p>
                            <p><strong>AI识别结果:</strong></p>
                            <div class="bg-gray-100 p-3 rounded mt-2">${content}</div>
                            <p class="mt-2 text-sm">🎉 现在可以在主应用中使用OpenRouter了！</p>
                        </div>
                    `;
                } else {
                    const errorText = await response.text();
                    let errorData;
                    try {
                        errorData = JSON.parse(errorText);
                    } catch (e) {
                        errorData = { error: { message: errorText } };
                    }
                    
                    resultDiv.innerHTML = `
                        <div class="text-red-600">
                            <h3 class="font-bold mb-2">❌ 测试失败</h3>
                            <p><strong>状态码:</strong> ${response.status}</p>
                            <p><strong>错误信息:</strong> ${errorData.error?.message || errorText}</p>
                            <div class="mt-3 text-sm">
                                <p><strong>可能原因:</strong></p>
                                <ul class="list-disc list-inside mt-1">
                                    <li>API Key格式错误（应该是sk-or-v1-开头）</li>
                                    <li>API Key无效或过期</li>
                                    <li>账户余额不足</li>
                                    <li>模型不可用</li>
                                </ul>
                                <p class="mt-2"><strong>解决方案:</strong></p>
                                <ul class="list-disc list-inside mt-1">
                                    <li>检查API Key是否正确复制</li>
                                    <li>确认在OpenRouter控制台中API Key状态正常</li>
                                    <li>尝试重新生成API Key</li>
                                </ul>
                            </div>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="text-red-600">
                        <h3 class="font-bold mb-2">❌ 网络错误</h3>
                        <p><strong>错误信息:</strong> ${error.message}</p>
                        <div class="mt-3 text-sm">
                            <p><strong>可能原因:</strong></p>
                            <ul class="list-disc list-inside mt-1">
                                <li>网络连接问题</li>
                                <li>CORS跨域限制</li>
                                <li>OpenRouter服务暂时不可用</li>
                                <li>浏览器安全策略限制</li>
                            </ul>
                            <p class="mt-2"><strong>建议:</strong></p>
                            <ul class="list-disc list-inside mt-1">
                                <li>检查网络连接</li>
                                <li>尝试刷新页面重试</li>
                                <li>使用不同的浏览器</li>
                                <li>稍后再试</li>
                            </ul>
                        </div>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
