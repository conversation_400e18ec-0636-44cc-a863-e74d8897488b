# OpenRouter 免费视觉模型使用指南

## 🎉 什么是OpenRouter？

OpenRouter是一个统一的AI API平台，提供多种AI模型的统一接口，包括**免费的视觉模型**！

### ✨ OpenRouter优势
- 🆓 **有免费模型**：Google Gemini Flash 1.5 免费使用
- 🔄 **统一API**：一个API Key访问多种模型
- 💰 **价格透明**：清晰的定价，按使用付费
- 🚀 **高可用性**：稳定的服务和快速响应
- 🌍 **无地区限制**：全球可用

## 🆓 免费模型推荐

### Google Gemini Flash 1.5 (推荐)
- **模型ID**: `google/gemini-flash-1.5`
- **费用**: 完全免费
- **特点**: 快速响应，视觉识别能力强
- **限制**: 每分钟有请求限制

### 其他可选模型
- **Claude 3 Haiku**: `anthropic/claude-3-haiku` (低成本)
- **GPT-4 Vision**: `openai/gpt-4-vision-preview` (高质量)
- **Gemini Pro Vision**: `google/gemini-pro-vision` (平衡选择)

## 🚀 快速开始

### 步骤1: 注册OpenRouter账号
1. 访问：https://openrouter.ai/
2. 点击"Sign Up"注册账号
3. 验证邮箱完成注册

### 步骤2: 获取API Key
1. 登录后进入Dashboard
2. 点击"Keys"标签页
3. 点击"Create Key"创建新的API密钥
4. 复制生成的API Key（格式：sk-or-v1-...）

### 步骤3: 在应用中配置
1. 打开智能背单词应用：http://localhost:8000
2. 点击"图片识别"按钮
3. 选择"OpenRouter (免费)"引擎
4. 输入您的API Key
5. 选择模型（推荐：Gemini Flash 1.5）
6. 开始使用！

## 🎯 演示模式

如果您想先体验效果：
1. 选择"OpenRouter (免费)"引擎
2. API Key输入：`demo`
3. 上传图片测试识别效果

## 💡 使用技巧

### 1. 模型选择建议
```
🆓 免费使用: google/gemini-flash-1.5
💰 低成本: anthropic/claude-3-haiku
🎯 高质量: openai/gpt-4-vision-preview
⚡ 快速: google/gemini-flash-1.5
```

### 2. 优化识别效果
- **图片质量**: 使用清晰、高分辨率的图片
- **文字大小**: 确保文字足够大，易于识别
- **背景简洁**: 避免复杂背景干扰
- **光线充足**: 确保图片光线均匀

### 3. 成本控制
- **免费模型**: 优先使用Gemini Flash 1.5
- **批量处理**: 一次识别多个词汇
- **监控用量**: 定期检查API使用情况

## 📊 模型对比

| 模型 | 费用 | 速度 | 准确率 | 特点 |
|------|------|------|--------|------|
| Gemini Flash 1.5 | 🆓 免费 | ⚡ 很快 | 📈 高 | 免费，快速 |
| Claude 3 Haiku | 💰 低 | ⚡ 快 | 📈 高 | 安全，稳定 |
| GPT-4 Vision | 💰 高 | 🐌 慢 | 📊 最高 | 最准确 |
| Gemini Pro Vision | 💰 中 | ⚡ 快 | 📈 高 | 平衡选择 |

## 🔧 配置示例

### 基础配置
```javascript
{
  "apiKey": "sk-or-v1-your-api-key",
  "baseUrl": "https://openrouter.ai/api/v1",
  "model": "google/gemini-flash-1.5"
}
```

### 高质量配置
```javascript
{
  "apiKey": "sk-or-v1-your-api-key",
  "baseUrl": "https://openrouter.ai/api/v1",
  "model": "openai/gpt-4-vision-preview"
}
```

## 🛠️ 故障排除

### 常见问题

#### Q: API Key无效？
A: 
- 确认API Key格式正确（sk-or-v1-开头）
- 检查是否复制完整
- 确认账户状态正常

#### Q: 请求失败？
A: 
- 检查网络连接
- 确认模型名称正确
- 查看是否达到免费限制

#### Q: 识别效果不好？
A: 
- 尝试不同的模型
- 优化图片质量
- 调整图片大小

### 错误代码说明
- **401**: API Key无效
- **429**: 请求频率超限
- **400**: 请求格式错误
- **500**: 服务器错误

## 💰 费用说明

### 免费额度
- **Gemini Flash 1.5**: 完全免费
- **每日限制**: 有一定的请求次数限制
- **无需绑卡**: 注册即可使用

### 付费模型
- **按使用计费**: 只为实际使用付费
- **透明定价**: 清晰的价格表
- **无月费**: 没有固定月费

## 🎉 成功案例

### 手写笔记识别
```
输入: 手写的英语单词表
输出: 
apple 苹果
banana 香蕉
orange 橙子
...
准确率: 95%+
```

### 教科书内容
```
输入: 教科书页面照片
输出: 完整的文字内容，保持原有格式
准确率: 98%+
```

### 练习册题目
```
输入: 作业本照片
输出: 题目和答案，智能分离
准确率: 90%+
```

## 🔮 高级功能

### 1. 自定义提示词
可以根据需要调整识别提示词，提高特定场景的识别效果。

### 2. 批量处理
一次上传多个图片，批量识别处理。

### 3. 结果后处理
自动格式化识别结果，智能配对中英文。

## 📞 获取支持

### OpenRouter官方
- **网站**: https://openrouter.ai/
- **文档**: https://openrouter.ai/docs
- **Discord**: 官方社区支持
- **邮箱**: <EMAIL>

### 本地调试
1. 打开浏览器开发者工具
2. 查看Network标签页的API请求
3. 检查Console中的错误信息
4. 复制详细错误信息寻求帮助

## 🎊 总结

OpenRouter为您提供了：
- 🆓 **免费的视觉识别能力**
- 🔄 **多种模型选择**
- 💰 **灵活的定价方案**
- 🚀 **简单的集成方式**

现在就开始使用OpenRouter，享受免费的AI视觉识别服务吧！

---

## 🚀 立即开始

1. **注册账号**: https://openrouter.ai/
2. **获取API Key**: 在Dashboard中创建
3. **配置应用**: 选择OpenRouter引擎
4. **开始识别**: 上传图片，体验效果

**让AI帮您轻松识别手写笔记和教科书内容！** 📚✨
