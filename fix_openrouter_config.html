<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenRouter配置修复工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold mb-6 text-center text-green-700">OpenRouter配置修复工具</h1>
        
        <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h2 class="font-bold text-yellow-800 mb-2">🔧 问题诊断</h2>
            <p class="text-yellow-700 text-sm">
                如果您在主应用中遇到"请先配置OpenRouter的API密钥"错误，
                这个工具将帮您直接修复配置问题。
            </p>
        </div>

        <!-- 配置输入 -->
        <div class="mb-6">
            <h2 class="text-lg font-semibold mb-3">输入您的OpenRouter配置</h2>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium mb-1">API Key:</label>
                    <input type="text" id="apiKey" placeholder="sk-or-v1-..." class="w-full px-3 py-2 border rounded-lg">
                    <div class="text-xs text-gray-500 mt-1">
                        没有API Key？<a href="https://openrouter.ai/" target="_blank" class="text-green-600 hover:underline">点击这里免费获取</a>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-1">模型:</label>
                    <select id="model" class="w-full px-3 py-2 border rounded-lg">
                        <option value="meta-llama/llama-3.2-11b-vision-instruct:free">Llama 3.2 11B Vision (免费)</option>
                        <option value="google/gemini-flash-1.5">Gemini Flash 1.5 (免费)</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="mb-6 space-y-3">
            <button id="fixConfig" class="w-full bg-green-500 hover:bg-green-600 text-white py-3 px-4 rounded-lg font-medium">
                🔧 修复配置并保存
            </button>
            <button id="testConfig" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-lg font-medium">
                🧪 测试配置
            </button>
            <button id="goToMainApp" class="w-full bg-purple-500 hover:bg-purple-600 text-white py-3 px-4 rounded-lg font-medium">
                🚀 前往主应用
            </button>
        </div>

        <!-- 状态显示 -->
        <div id="status" class="mb-6">
            <div class="bg-gray-50 p-4 rounded-lg">
                <p class="text-gray-500">点击"修复配置"开始...</p>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h3 class="font-bold text-blue-800 mb-2">📋 使用说明</h3>
            <ol class="text-sm text-blue-700 space-y-1 list-decimal list-inside">
                <li>输入您的OpenRouter API Key</li>
                <li>选择要使用的模型（推荐Llama 3.2免费模型）</li>
                <li>点击"修复配置并保存"</li>
                <li>测试配置是否正常工作</li>
                <li>前往主应用开始使用</li>
            </ol>
        </div>
    </div>

    <script>
        // 事件监听
        document.getElementById('fixConfig').addEventListener('click', fixConfig);
        document.getElementById('testConfig').addEventListener('click', testConfig);
        document.getElementById('goToMainApp').addEventListener('click', () => {
            window.location.href = 'index.html';
        });

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const colors = {
                'success': 'text-green-600 bg-green-50 border border-green-200',
                'error': 'text-red-600 bg-red-50 border border-red-200',
                'info': 'text-blue-600 bg-blue-50 border border-blue-200',
                'warning': 'text-yellow-600 bg-yellow-50 border border-yellow-200'
            };
            
            statusDiv.innerHTML = `<div class="p-4 rounded-lg ${colors[type]}">${message}</div>`;
        }

        function fixConfig() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const model = document.getElementById('model').value;

            if (!apiKey) {
                showStatus('❌ 请输入API Key', 'error');
                return;
            }

            try {
                // 直接操作localStorage，绕过可能的模块问题
                const config = {
                    openrouter: {
                        apiKey: apiKey,
                        baseUrl: 'https://openrouter.ai/api/v1',
                        model: model
                    }
                };

                // 保存到localStorage
                localStorage.setItem('llmOcrConfigs', JSON.stringify(config));

                // 验证保存
                const saved = localStorage.getItem('llmOcrConfigs');
                const parsed = JSON.parse(saved);

                if (parsed && parsed.openrouter && parsed.openrouter.apiKey === apiKey) {
                    showStatus(`
                        ✅ 配置修复成功！<br>
                        <strong>API Key:</strong> ${apiKey.substring(0, 20)}...<br>
                        <strong>模型:</strong> ${model}<br>
                        <strong>状态:</strong> 已保存到本地存储<br>
                        <br>
                        现在可以测试配置或前往主应用使用。
                    `, 'success');
                } else {
                    throw new Error('配置验证失败');
                }

            } catch (error) {
                showStatus(`❌ 配置修复失败: ${error.message}`, 'error');
            }
        }

        async function testConfig() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const model = document.getElementById('model').value;

            if (!apiKey) {
                showStatus('❌ 请先输入API Key并修复配置', 'error');
                return;
            }

            if (apiKey === 'demo') {
                showStatus(`
                    ✅ 演示模式测试成功！<br>
                    <strong>模式:</strong> 演示模式<br>
                    <strong>效果:</strong> 将显示模拟识别结果<br>
                    <br>
                    现在可以前往主应用使用演示功能。
                `, 'success');
                return;
            }

            showStatus('🔄 正在测试API连接...', 'info');

            try {
                // 创建测试图片
                const canvas = document.createElement('canvas');
                canvas.width = 200;
                canvas.height = 100;
                const ctx = canvas.getContext('2d');
                
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, 200, 100);
                ctx.fillStyle = 'black';
                ctx.font = '16px Arial';
                ctx.fillText('Test 测试', 20, 50);
                
                const imageBase64 = canvas.toDataURL();

                const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify({
                        model: model,
                        messages: [
                            {
                                role: 'user',
                                content: [
                                    {
                                        type: 'text',
                                        text: 'Please identify the text in this image'
                                    },
                                    {
                                        type: 'image_url',
                                        image_url: {
                                            url: imageBase64
                                        }
                                    }
                                ]
                            }
                        ],
                        max_tokens: 100,
                        temperature: 0.1
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const content = data.choices[0].message.content;
                    
                    showStatus(`
                        ✅ API测试成功！<br>
                        <strong>模型:</strong> ${model}<br>
                        <strong>识别结果:</strong> ${content}<br>
                        <strong>状态:</strong> 配置正常工作<br>
                        <br>
                        🎉 现在可以前往主应用正常使用了！
                    `, 'success');
                } else {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

            } catch (error) {
                showStatus(`
                    ❌ API测试失败<br>
                    <strong>错误:</strong> ${error.message}<br>
                    <br>
                    <strong>可能原因:</strong><br>
                    • API Key无效或过期<br>
                    • 网络连接问题<br>
                    • 模型不可用<br>
                    <br>
                    请检查API Key是否正确，或尝试演示模式。
                `, 'error');
            }
        }

        // 页面加载时检查现有配置
        window.addEventListener('load', () => {
            try {
                const saved = localStorage.getItem('llmOcrConfigs');
                if (saved) {
                    const config = JSON.parse(saved);
                    if (config.openrouter) {
                        document.getElementById('apiKey').value = config.openrouter.apiKey || '';
                        document.getElementById('model').value = config.openrouter.model || 'meta-llama/llama-3.2-11b-vision-instruct:free';
                        
                        showStatus(`
                            ℹ️ 检测到现有配置<br>
                            <strong>API Key:</strong> ${config.openrouter.apiKey ? '已设置' : '未设置'}<br>
                            <strong>模型:</strong> ${config.openrouter.model || '默认'}<br>
                            <br>
                            您可以修改配置或直接测试。
                        `, 'info');
                    }
                }
            } catch (error) {
                console.error('加载现有配置失败:', error);
            }
        });
    </script>
</body>
</html>
