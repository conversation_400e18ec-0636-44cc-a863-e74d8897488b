<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM OCR功能测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="js/llm-ocr.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-3xl font-bold mb-6 text-center">LLM OCR功能测试</h1>
        
        <!-- 引擎选择 -->
        <div class="mb-6">
            <h2 class="text-xl font-semibold mb-3">选择LLM引擎:</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <label class="flex items-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-purple-300">
                    <input type="radio" name="llmEngine" value="deepseek" checked class="mr-3">
                    <div>
                        <div class="font-medium">DeepSeek</div>
                        <div class="text-xs text-gray-500">推荐</div>
                    </div>
                </label>
                <label class="flex items-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-purple-300">
                    <input type="radio" name="llmEngine" value="openai" class="mr-3">
                    <div>
                        <div class="font-medium">GPT-4</div>
                        <div class="text-xs text-gray-500">最强</div>
                    </div>
                </label>
                <label class="flex items-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-purple-300">
                    <input type="radio" name="llmEngine" value="claude" class="mr-3">
                    <div>
                        <div class="font-medium">Claude 3</div>
                        <div class="text-xs text-gray-500">安全</div>
                    </div>
                </label>
                <label class="flex items-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-purple-300">
                    <input type="radio" name="llmEngine" value="qwen" class="mr-3">
                    <div>
                        <div class="font-medium">Qwen-VL</div>
                        <div class="text-xs text-gray-500">中文</div>
                    </div>
                </label>
            </div>
        </div>

        <!-- API配置 -->
        <div class="mb-6">
            <h2 class="text-xl font-semibold mb-3">API配置:</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <input type="text" id="apiKey" placeholder="API Key (输入 'demo' 体验演示)" class="px-4 py-2 border border-gray-300 rounded-lg">
                <input type="text" id="baseUrl" placeholder="Base URL (可选)" class="px-4 py-2 border border-gray-300 rounded-lg">
            </div>
            <div class="mt-2 text-sm text-gray-600">
                💡 提示: 输入 "demo" 作为API Key可以体验演示效果
            </div>
        </div>

        <!-- 图片上传 -->
        <div class="mb-6">
            <h2 class="text-xl font-semibold mb-3">上传图片:</h2>
            <input type="file" id="imageInput" accept="image/*" class="mb-3">
            <div id="imagePreview" class="hidden mb-3">
                <img id="previewImg" class="max-w-full h-auto border rounded max-h-64">
            </div>
            <button id="generateSample" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded mr-2">
                生成测试图片
            </button>
            <button id="startRecognition" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded" disabled>
                开始LLM识别
            </button>
        </div>

        <!-- 测试图片生成 -->
        <div class="mb-6 hidden" id="canvasSection">
            <canvas id="testCanvas" width="400" height="300" class="border rounded"></canvas>
        </div>

        <!-- 进度显示 -->
        <div id="progress" class="mb-6 hidden">
            <div class="bg-purple-100 border border-purple-300 rounded p-4">
                <div class="flex items-center">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600 mr-3"></div>
                    <span id="progressText">正在使用LLM识别...</span>
                </div>
            </div>
        </div>

        <!-- 识别结果 -->
        <div id="results" class="hidden">
            <h2 class="text-xl font-semibold mb-3">LLM识别结果:</h2>
            <div class="mb-4">
                <label class="block text-sm font-medium mb-1">原始识别结果:</label>
                <textarea id="rawResult" rows="6" class="w-full p-3 border rounded" readonly></textarea>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium mb-1">智能配对结果:</label>
                <textarea id="pairedResult" rows="8" class="w-full p-3 border rounded"></textarea>
            </div>
            <div class="text-sm text-gray-600">
                <strong>识别引擎:</strong> <span id="usedEngine"></span><br>
                <strong>处理时间:</strong> <span id="processingTime"></span><br>
                <strong>识别质量:</strong> <span id="qualityScore"></span>
            </div>
        </div>
    </div>

    <script>
        let llmOcrManager;
        let startTime;

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            llmOcrManager = new LLMOCRManager();
            
            // 事件监听
            document.getElementById('imageInput').addEventListener('change', handleImageUpload);
            document.getElementById('generateSample').addEventListener('click', generateSampleImage);
            document.getElementById('startRecognition').addEventListener('click', startLLMRecognition);
            
            // 引擎选择变化
            document.querySelectorAll('input[name="llmEngine"]').forEach(radio => {
                radio.addEventListener('change', updateBaseUrl);
            });
            
            // 初始化Base URL
            updateBaseUrl();
        });

        function updateBaseUrl() {
            const selectedEngine = document.querySelector('input[name="llmEngine"]:checked').value;
            const baseUrlInput = document.getElementById('baseUrl');
            
            const defaultUrls = {
                'deepseek': 'https://api.deepseek.com/v1',
                'openai': 'https://api.openai.com/v1',
                'claude': 'https://api.anthropic.com',
                'qwen': 'https://dashscope.aliyuncs.com/api/v1'
            };
            
            baseUrlInput.value = defaultUrls[selectedEngine] || '';
        }

        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById('imagePreview');
                const img = document.getElementById('previewImg');
                img.src = e.target.result;
                preview.classList.remove('hidden');
                document.getElementById('startRecognition').disabled = false;
            };
            reader.readAsDataURL(file);
        }

        function generateSampleImage() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 设置字体和样式
            ctx.fillStyle = 'black';
            ctx.font = '20px Arial';
            
            // 绘制中英文词汇
            const words = [
                '苹果 apple',
                '香蕉 banana',
                '橙子 orange',
                '葡萄 grape',
                '草莓 strawberry',
                '西瓜 watermelon',
                '桃子 peach',
                '梨子 pear'
            ];
            
            words.forEach((word, index) => {
                ctx.fillText(word, 20, 30 + index * 35);
            });
            
            // 显示画布
            document.getElementById('canvasSection').classList.remove('hidden');
            
            // 转换为图片并设置到预览
            canvas.toBlob(blob => {
                const url = URL.createObjectURL(blob);
                const img = document.getElementById('previewImg');
                img.src = url;
                document.getElementById('imagePreview').classList.remove('hidden');
                document.getElementById('startRecognition').disabled = false;
            });
        }

        async function startLLMRecognition() {
            const selectedEngine = document.querySelector('input[name="llmEngine"]:checked').value;
            const apiKey = document.getElementById('apiKey').value.trim();
            const baseUrl = document.getElementById('baseUrl').value.trim();
            
            if (!apiKey) {
                alert('请输入API Key（或输入"demo"体验演示）');
                return;
            }

            const img = document.getElementById('previewImg');
            if (!img.src) {
                alert('请先上传图片或生成测试图片');
                return;
            }

            try {
                // 更新配置
                llmOcrManager.updateApiConfig(selectedEngine, {
                    apiKey: apiKey,
                    baseUrl: baseUrl
                });

                // 显示进度
                document.getElementById('progress').classList.remove('hidden');
                document.getElementById('results').classList.add('hidden');
                document.getElementById('startRecognition').disabled = true;
                
                startTime = Date.now();

                // 获取图片的base64数据
                const imageBase64 = await getImageAsBase64(img);

                // 开始LLM识别
                const result = await llmOcrManager.recognize(selectedEngine, imageBase64);

                // 显示结果
                displayResults(result, selectedEngine);

            } catch (error) {
                console.error('LLM识别失败:', error);
                alert(`识别失败: ${error.message}`);
            } finally {
                document.getElementById('progress').classList.add('hidden');
                document.getElementById('startRecognition').disabled = false;
            }
        }

        function getImageAsBase64(img) {
            return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = img.naturalWidth;
                canvas.height = img.naturalHeight;
                ctx.drawImage(img, 0, 0);
                resolve(canvas.toDataURL());
            });
        }

        function displayResults(result, engine) {
            const endTime = Date.now();
            const processingTime = ((endTime - startTime) / 1000).toFixed(1);

            document.getElementById('rawResult').value = result;
            document.getElementById('pairedResult').value = parseAndPairWords(result);
            document.getElementById('usedEngine').textContent = getEngineDisplayName(engine);
            document.getElementById('processingTime').textContent = `${processingTime} 秒`;
            document.getElementById('qualityScore').textContent = evaluateQuality(result);
            
            document.getElementById('results').classList.remove('hidden');
        }

        function parseAndPairWords(text) {
            // 简单的中英文配对逻辑
            const lines = text.split('\n').filter(line => line.trim());
            const pairedWords = [];

            for (const line of lines) {
                const words = line.trim().split(/\s+/);
                const chineseWords = [];
                const englishWords = [];

                for (const word of words) {
                    if (/[\u4e00-\u9fa5]/.test(word)) {
                        chineseWords.push(word);
                    } else if (/[a-zA-Z]/.test(word)) {
                        englishWords.push(word);
                    }
                }

                const maxPairs = Math.min(chineseWords.length, englishWords.length);
                for (let i = 0; i < maxPairs; i++) {
                    pairedWords.push(`${chineseWords[i]} ${englishWords[i]}`);
                }
            }

            return pairedWords.join('\n');
        }

        function getEngineDisplayName(engine) {
            const names = {
                'deepseek': 'DeepSeek Vision',
                'openai': 'GPT-4 Vision',
                'claude': 'Claude 3 Vision',
                'qwen': 'Qwen-VL'
            };
            return names[engine] || engine;
        }

        function evaluateQuality(result) {
            const wordCount = result.split(/\s+/).length;
            const lineCount = result.split('\n').length;
            
            if (wordCount > 10 && lineCount > 3) {
                return '优秀 ⭐⭐⭐⭐⭐';
            } else if (wordCount > 5) {
                return '良好 ⭐⭐⭐⭐';
            } else {
                return '一般 ⭐⭐⭐';
            }
        }
    </script>
</body>
</html>
