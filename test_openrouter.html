<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenRouter免费视觉模型测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-3xl font-bold mb-6 text-center text-green-700">
            🆓 OpenRouter免费视觉模型测试
        </h1>
        
        <!-- 配置区域 -->
        <div class="mb-6 p-4 bg-green-50 rounded-lg border border-green-200">
            <h2 class="text-xl font-semibold mb-3 text-green-800">API配置</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-1">OpenRouter API Key:</label>
                    <input type="text" id="apiKey" placeholder="sk-or-v1-... 或输入 'demo' 体验" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-1">选择模型:</label>
                    <select id="modelSelect" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                        <option value="google/gemini-flash-1.5">🆓 Gemini Flash 1.5 (免费)</option>
                        <option value="google/gemini-pro-vision">Gemini Pro Vision</option>
                        <option value="anthropic/claude-3-haiku">Claude 3 Haiku (低成本)</option>
                        <option value="openai/gpt-4-vision-preview">GPT-4 Vision (高质量)</option>
                    </select>
                </div>
            </div>
            <div class="mt-3 text-sm text-green-700">
                💡 没有API Key？访问 <a href="https://openrouter.ai/" target="_blank" class="text-green-600 hover:underline font-medium">OpenRouter官网</a> 免费注册获取
            </div>
        </div>

        <!-- 测试按钮 -->
        <div class="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <button id="testConnection" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg font-medium">
                🔗 测试连接
            </button>
            <button id="generateTestImage" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-3 rounded-lg font-medium">
                🖼️ 生成测试图片
            </button>
            <button id="testVision" class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg font-medium" disabled>
                👁️ 测试视觉识别
            </button>
        </div>

        <!-- 图片上传区域 -->
        <div class="mb-6">
            <h3 class="text-lg font-semibold mb-3">图片上传</h3>
            <input type="file" id="imageInput" accept="image/*" class="mb-3">
            <div id="imagePreview" class="hidden">
                <img id="previewImg" class="max-w-full h-auto border rounded max-h-64">
            </div>
        </div>

        <!-- 测试画布 -->
        <div class="mb-6 hidden" id="canvasSection">
            <h3 class="text-lg font-semibold mb-3">生成的测试图片</h3>
            <canvas id="testCanvas" width="400" height="200" class="border rounded"></canvas>
        </div>

        <!-- 结果显示 -->
        <div id="results" class="mb-6">
            <h3 class="text-lg font-semibold mb-3">测试结果</h3>
            <div id="resultContent" class="bg-gray-50 p-4 rounded-lg min-h-32">
                <p class="text-gray-500">点击上方按钮开始测试...</p>
            </div>
        </div>

        <!-- 使用指南 -->
        <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h3 class="text-lg font-semibold mb-3 text-blue-800">📚 使用指南</h3>
            <div class="text-sm text-blue-700 space-y-2">
                <p><strong>1. 获取API Key:</strong> 访问 <a href="https://openrouter.ai/" target="_blank" class="underline">OpenRouter官网</a> 注册并获取免费API Key</p>
                <p><strong>2. 选择免费模型:</strong> Gemini Flash 1.5 完全免费，无需绑定信用卡</p>
                <p><strong>3. 测试识别:</strong> 上传包含文字的图片，体验AI识别效果</p>
                <p><strong>4. 演示模式:</strong> 输入 "demo" 作为API Key可以体验模拟效果</p>
            </div>
        </div>
    </div>

    <script>
        // 事件监听
        document.getElementById('testConnection').addEventListener('click', testConnection);
        document.getElementById('generateTestImage').addEventListener('click', generateTestImage);
        document.getElementById('testVision').addEventListener('click', testVision);
        document.getElementById('imageInput').addEventListener('change', handleImageUpload);

        function showResult(content, type = 'info') {
            const resultDiv = document.getElementById('resultContent');
            const colors = {
                'success': 'text-green-600 bg-green-50 border-green-200',
                'error': 'text-red-600 bg-red-50 border-red-200',
                'info': 'text-blue-600 bg-blue-50 border-blue-200',
                'warning': 'text-yellow-600 bg-yellow-50 border-yellow-200'
            };
            
            resultDiv.innerHTML = `<div class="p-3 rounded border ${colors[type]}">${content}</div>`;
        }

        function showLoading(message) {
            const resultDiv = document.getElementById('resultContent');
            resultDiv.innerHTML = `
                <div class="flex items-center text-blue-600">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3"></div>
                    <span>${message}</span>
                </div>
            `;
        }

        async function testConnection() {
            const apiKey = document.getElementById('apiKey').value.trim();
            
            if (!apiKey) {
                showResult('请输入OpenRouter API Key', 'error');
                return;
            }

            if (apiKey === 'demo') {
                showResult(`
                    <h3 class="font-bold mb-2">✅ 演示模式</h3>
                    <p>您正在使用演示模式，可以体验模拟的识别效果。</p>
                    <p>要使用真实的OpenRouter API，请访问 <a href="https://openrouter.ai/" target="_blank" class="underline">OpenRouter官网</a> 获取免费API Key。</p>
                `, 'success');
                return;
            }

            showLoading('测试OpenRouter连接...');

            try {
                const response = await fetch('https://openrouter.ai/api/v1/models', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'HTTP-Referer': window.location.origin,
                        'X-Title': 'Smart Vocabulary App'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    const visionModels = data.data.filter(model => 
                        model.id.includes('vision') || 
                        model.id.includes('gemini') || 
                        model.id.includes('claude-3')
                    );
                    
                    showResult(`
                        <h3 class="font-bold mb-2 text-green-600">✅ 连接成功！</h3>
                        <p><strong>状态码:</strong> ${response.status}</p>
                        <p><strong>可用模型总数:</strong> ${data.data.length}</p>
                        <p><strong>视觉模型数量:</strong> ${visionModels.length}</p>
                        <p class="mt-2 text-sm">现在可以测试视觉识别功能了！</p>
                    `, 'success');
                } else {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
            } catch (error) {
                showResult(`
                    <h3 class="font-bold mb-2">❌ 连接失败</h3>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                    <p class="mt-2"><strong>可能原因:</strong></p>
                    <ul class="list-disc list-inside mt-1 text-sm">
                        <li>API Key无效或格式错误</li>
                        <li>网络连接问题</li>
                        <li>OpenRouter服务暂时不可用</li>
                    </ul>
                `, 'error');
            }
        }

        function generateTestImage() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 设置字体和样式
            ctx.fillStyle = 'black';
            ctx.font = '18px Arial';
            
            // 绘制中英文词汇
            const words = [
                '苹果 apple',
                '香蕉 banana',
                '橙子 orange',
                '葡萄 grape',
                '草莓 strawberry'
            ];
            
            words.forEach((word, index) => {
                ctx.fillText(word, 20, 30 + index * 30);
            });
            
            // 显示画布
            document.getElementById('canvasSection').classList.remove('hidden');
            
            // 转换为图片并设置到预览
            canvas.toBlob(blob => {
                const url = URL.createObjectURL(blob);
                const img = document.getElementById('previewImg');
                img.src = url;
                document.getElementById('imagePreview').classList.remove('hidden');
                document.getElementById('testVision').disabled = false;
            });

            showResult('✅ 测试图片生成成功！现在可以测试视觉识别了。', 'success');
        }

        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const img = document.getElementById('previewImg');
                img.src = e.target.result;
                document.getElementById('imagePreview').classList.remove('hidden');
                document.getElementById('testVision').disabled = false;
            };
            reader.readAsDataURL(file);
        }

        async function testVision() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const model = document.getElementById('modelSelect').value;
            const img = document.getElementById('previewImg');

            if (!apiKey) {
                showResult('请输入OpenRouter API Key', 'error');
                return;
            }

            if (!img.src) {
                showResult('请先上传图片或生成测试图片', 'error');
                return;
            }

            // 演示模式
            if (apiKey === 'demo') {
                showLoading('模拟OpenRouter识别中...');
                
                setTimeout(() => {
                    showResult(`
                        <h3 class="font-bold mb-2 text-green-600">✅ 演示识别成功！</h3>
                        <p><strong>使用模型:</strong> ${model}</p>
                        <p><strong>识别结果:</strong></p>
                        <div class="bg-gray-100 p-3 rounded mt-2 font-mono text-sm">
苹果 apple<br>
香蕉 banana<br>
橙子 orange<br>
葡萄 grape<br>
草莓 strawberry
                        </div>
                        <p class="mt-2 text-sm text-gray-600">这是演示效果。要使用真实API，请获取OpenRouter API Key。</p>
                    `, 'success');
                }, 3000);
                return;
            }

            showLoading(`使用 ${model} 识别中...`);

            try {
                // 获取图片的base64
                const imageBase64 = await getImageAsBase64(img);

                const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`,
                        'HTTP-Referer': window.location.origin,
                        'X-Title': 'Smart Vocabulary App'
                    },
                    body: JSON.stringify({
                        model: model,
                        messages: [
                            {
                                role: 'user',
                                content: [
                                    {
                                        type: 'text',
                                        text: '请仔细识别图片中的所有文字内容，包括中文和英文。保持原有的文字布局和换行。不要添加任何解释，只输出识别到的文字内容。'
                                    },
                                    {
                                        type: 'image_url',
                                        image_url: {
                                            url: imageBase64
                                        }
                                    }
                                ]
                            }
                        ],
                        max_tokens: 1000,
                        temperature: 0.1
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const content = data.choices[0].message.content;
                    
                    showResult(`
                        <h3 class="font-bold mb-2 text-green-600">✅ 识别成功！</h3>
                        <p><strong>使用模型:</strong> ${model}</p>
                        <p><strong>消耗Token:</strong> ${data.usage ? data.usage.total_tokens : '未知'}</p>
                        <p><strong>识别结果:</strong></p>
                        <div class="bg-gray-100 p-3 rounded mt-2 font-mono text-sm whitespace-pre-wrap">${content}</div>
                        <p class="mt-2 text-sm text-green-600">🎉 OpenRouter视觉识别测试成功！现在可以在主应用中使用了。</p>
                    `, 'success');
                } else {
                    const errorText = await response.text();
                    let errorData;
                    try {
                        errorData = JSON.parse(errorText);
                    } catch (e) {
                        errorData = { error: { message: errorText } };
                    }
                    
                    throw new Error(`HTTP ${response.status}: ${errorData.error?.message || errorText}`);
                }
            } catch (error) {
                showResult(`
                    <h3 class="font-bold mb-2">❌ 识别失败</h3>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                    <p class="mt-2"><strong>可能原因:</strong></p>
                    <ul class="list-disc list-inside mt-1 text-sm">
                        <li>API Key无效或余额不足</li>
                        <li>选择的模型不支持视觉功能</li>
                        <li>图片格式或大小问题</li>
                        <li>网络连接问题</li>
                    </ul>
                    <p class="mt-2"><strong>建议:</strong></p>
                    <ul class="list-disc list-inside mt-1 text-sm">
                        <li>确认API Key正确</li>
                        <li>尝试免费的Gemini Flash 1.5模型</li>
                        <li>检查图片大小（建议小于5MB）</li>
                    </ul>
                `, 'error');
            }
        }

        function getImageAsBase64(img) {
            return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = img.naturalWidth;
                canvas.height = img.naturalHeight;
                ctx.drawImage(img, 0, 0);
                resolve(canvas.toDataURL());
            });
        }
    </script>
</body>
</html>
