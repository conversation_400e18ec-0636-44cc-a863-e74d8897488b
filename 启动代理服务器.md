# 启动百度OCR代理服务器

## 为什么需要代理服务器？

由于浏览器的CORS（跨域资源共享）安全策略，直接从网页调用百度API会被阻止。代理服务器可以解决这个问题。

## 快速启动

### 方法一：使用Node.js（推荐）

1. **安装Node.js**
   - 访问 https://nodejs.org/ 下载并安装Node.js
   - 验证安装：在命令行运行 `node --version`

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动代理服务器**
   ```bash
   npm start
   ```
   
   或者直接运行：
   ```bash
   node baidu-ocr-proxy.js
   ```

4. **验证服务器**
   - 浏览器访问：http://localhost:3001/health
   - 看到 `{"status":"ok","message":"百度OCR代理服务运行正常"}` 表示成功

### 方法二：使用Python（备选）

如果您更熟悉Python，可以使用以下简单代理：

```python
# baidu_proxy.py
from flask import Flask, request, jsonify
from flask_cors import CORS
import requests

app = Flask(__name__)
CORS(app)

@app.route('/api/baidu/token', methods=['POST'])
def get_token():
    data = request.json
    response = requests.post('https://aip.baidubce.com/oauth/2.0/token', data={
        'grant_type': 'client_credentials',
        'client_id': data['apiKey'],
        'client_secret': data['secretKey']
    })
    return jsonify(response.json())

@app.route('/api/baidu/handwriting', methods=['POST'])
def ocr_handwriting():
    data = request.json
    response = requests.post(
        f'https://aip.baidubce.com/rest/2.0/ocr/v1/handwriting?access_token={data["accessToken"]}',
        data={'image': data['image'], 'recognize_granularity': 'big'}
    )
    return jsonify(response.json())

if __name__ == '__main__':
    app.run(port=3001, debug=True)
```

启动Python代理：
```bash
pip install flask flask-cors requests
python baidu_proxy.py
```

## 使用步骤

1. **启动代理服务器**（选择上述任一方法）

2. **配置百度API**
   - 在应用中选择"百度AI"引擎
   - 输入您的API Key：`29GK89PIpJMUZU7G1oOOvhZG`
   - 输入您的Secret Key：`XwBtrcoU1yOFbhqGIN0BuewqtfGI6RHT`

3. **开始识别**
   - 上传图片
   - 点击"开始识别"
   - 系统会自动使用代理服务器

## 故障排除

### 问题1：端口被占用
```
Error: listen EADDRINUSE: address already in use :::3001
```

**解决方案：**
- 更改端口号：编辑 `baidu-ocr-proxy.js`，将 `PORT = 3001` 改为其他端口
- 或者杀死占用端口的进程

### 问题2：依赖安装失败
```
npm ERR! code ENOTFOUND
```

**解决方案：**
- 检查网络连接
- 使用国内镜像：`npm config set registry https://registry.npm.taobao.org/`
- 重新运行 `npm install`

### 问题3：API密钥错误
```
百度API错误: invalid_client
```

**解决方案：**
- 检查API Key和Secret Key是否正确
- 确认百度云账号已开通OCR服务
- 检查API密钥是否有效期内

### 问题4：识别失败
```
无法连接到百度API服务
```

**解决方案：**
- 确认代理服务器正在运行
- 检查网络连接
- 尝试重启代理服务器

## 安全说明

1. **本地运行**：代理服务器运行在您的本地计算机上，不会泄露API密钥
2. **仅代理**：服务器只是转发请求，不存储任何数据
3. **开源透明**：所有代码都是开源的，您可以查看和修改

## 高级配置

### 自定义端口
编辑 `baidu-ocr-proxy.js`：
```javascript
const PORT = 3001; // 改为您想要的端口号
```

### 添加日志
编辑 `baidu-ocr-proxy.js`，在相应位置添加：
```javascript
console.log('请求详情:', req.body);
console.log('响应详情:', result);
```

### 启用HTTPS
如果需要HTTPS支持，可以添加SSL证书配置。

## 无代理服务器的替代方案

如果无法启动代理服务器，可以尝试：

1. **使用演示模式**
   - API Key输入：`demo`
   - Secret Key输入：`demo`
   - 体验模拟识别效果

2. **浏览器扩展**
   - 安装CORS禁用扩展（仅用于开发测试）
   - 注意：这会降低浏览器安全性

3. **使用Tesseract本地识别**
   - 选择"Tesseract (本地)"引擎
   - 虽然手写体效果较差，但无需网络

---

**启动代理服务器后，您就可以享受高精度的AI手写体识别了！** 🚀
