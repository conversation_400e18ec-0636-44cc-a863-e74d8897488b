<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR功能测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/tesseract.js@4/dist/tesseract.min.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold mb-6">OCR功能测试</h1>
        
        <!-- 测试图片生成区域 -->
        <div class="mb-6">
            <h2 class="text-lg font-semibold mb-3">生成测试图片</h2>
            <canvas id="testCanvas" width="400" height="200" class="border border-gray-300 rounded mb-3"></canvas>
            <button id="generateTestImage" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                生成测试图片
            </button>
            <button id="downloadTestImage" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded ml-2">
                下载图片
            </button>
        </div>
        
        <!-- 图片上传区域 -->
        <div class="mb-6">
            <h2 class="text-lg font-semibold mb-3">上传图片进行OCR识别</h2>
            <input type="file" id="imageInput" accept="image/*" class="mb-3">
            <div id="imagePreview" class="mb-3 hidden">
                <img id="previewImg" class="max-w-full h-auto border rounded">
            </div>
            <button id="startOCR" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded" disabled>
                开始OCR识别
            </button>
        </div>
        
        <!-- 进度显示 -->
        <div id="progress" class="mb-6 hidden">
            <div class="bg-blue-100 border border-blue-300 rounded p-3">
                <div class="flex items-center">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                    <span id="progressText">正在初始化...</span>
                </div>
            </div>
        </div>
        
        <!-- 识别结果 -->
        <div id="results" class="hidden">
            <h2 class="text-lg font-semibold mb-3">识别结果</h2>
            <div class="mb-3">
                <label class="block text-sm font-medium mb-1">原始文本:</label>
                <textarea id="rawText" rows="4" class="w-full p-2 border rounded" readonly></textarea>
            </div>
            <div class="mb-3">
                <label class="block text-sm font-medium mb-1">智能配对结果:</label>
                <textarea id="pairedText" rows="6" class="w-full p-2 border rounded"></textarea>
            </div>
        </div>
    </div>

    <script>
        let worker = null;
        let isInitialized = false;

        // 生成测试图片
        document.getElementById('generateTestImage').addEventListener('click', generateTestImage);
        document.getElementById('downloadTestImage').addEventListener('click', downloadTestImage);
        document.getElementById('imageInput').addEventListener('change', handleImageUpload);
        document.getElementById('startOCR').addEventListener('click', startOCR);

        function generateTestImage() {
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 设置字体和样式
            ctx.fillStyle = 'black';
            ctx.font = '24px Arial';
            
            // 绘制中英文文字
            const words = [
                '苹果 apple',
                '香蕉 banana', 
                '橙子 orange',
                '葡萄 grape',
                '草莓 strawberry'
            ];
            
            words.forEach((word, index) => {
                ctx.fillText(word, 20, 30 + index * 35);
            });
        }

        function downloadTestImage() {
            const canvas = document.getElementById('testCanvas');
            const link = document.createElement('a');
            link.download = 'test-words.png';
            link.href = canvas.toDataURL();
            link.click();
        }

        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById('imagePreview');
                const img = document.getElementById('previewImg');
                img.src = e.target.result;
                preview.classList.remove('hidden');
                document.getElementById('startOCR').disabled = false;
            };
            reader.readAsDataURL(file);
        }

        async function initializeOCR() {
            if (isInitialized) return;

            try {
                document.getElementById('progress').classList.remove('hidden');
                document.getElementById('progressText').textContent = '正在初始化OCR引擎...';

                worker = await Tesseract.createWorker({
                    logger: m => {
                        if (m.status === 'recognizing text') {
                            const progress = Math.round(m.progress * 100);
                            document.getElementById('progressText').textContent = `识别中... ${progress}%`;
                        }
                    }
                });

                await worker.loadLanguage('eng+chi_sim');
                await worker.initialize('eng+chi_sim');
                
                isInitialized = true;
                console.log('OCR引擎初始化成功');
            } catch (error) {
                console.error('OCR引擎初始化失败:', error);
                alert('OCR引擎初始化失败: ' + error.message);
            }
        }

        async function startOCR() {
            const imageInput = document.getElementById('imageInput');
            const file = imageInput.files[0];
            if (!file) {
                alert('请先选择图片');
                return;
            }

            try {
                document.getElementById('progress').classList.remove('hidden');
                document.getElementById('startOCR').disabled = true;

                // 初始化OCR引擎
                if (!isInitialized) {
                    await initializeOCR();
                }

                // 开始识别
                document.getElementById('progressText').textContent = '正在识别文字...';
                const { data: { text } } = await worker.recognize(file);

                // 显示结果
                document.getElementById('rawText').value = text;
                document.getElementById('pairedText').value = parseAndMatchWords(text);
                document.getElementById('results').classList.remove('hidden');
                
                alert('识别完成！');
            } catch (error) {
                console.error('OCR识别失败:', error);
                alert('OCR识别失败: ' + error.message);
            } finally {
                document.getElementById('progress').classList.add('hidden');
                document.getElementById('startOCR').disabled = false;
            }
        }

        function parseAndMatchWords(text) {
            // 清理文本
            const cleanText = text.replace(/[^\u4e00-\u9fa5a-zA-Z\s\n]/g, ' ')
                                 .replace(/\s+/g, ' ')
                                 .trim();

            const lines = cleanText.split('\n').map(line => line.trim()).filter(line => line);
            const pairedWords = [];

            for (const line of lines) {
                const words = line.split(/\s+/);
                const chineseWords = [];
                const englishWords = [];

                // 分离中英文
                for (const word of words) {
                    if (/[\u4e00-\u9fa5]/.test(word)) {
                        chineseWords.push(word);
                    } else if (/[a-zA-Z]/.test(word)) {
                        englishWords.push(word);
                    }
                }

                // 尝试配对
                const maxPairs = Math.min(chineseWords.length, englishWords.length);
                for (let i = 0; i < maxPairs; i++) {
                    pairedWords.push(`${chineseWords[i]} ${englishWords[i]}`);
                }

                // 处理剩余的单词
                if (chineseWords.length > englishWords.length) {
                    for (let i = maxPairs; i < chineseWords.length; i++) {
                        pairedWords.push(`${chineseWords[i]} `);
                    }
                } else if (englishWords.length > chineseWords.length) {
                    for (let i = maxPairs; i < englishWords.length; i++) {
                        pairedWords.push(` ${englishWords[i]}`);
                    }
                }
            }

            return pairedWords.join('\n');
        }

        // 页面加载时生成测试图片
        window.addEventListener('load', generateTestImage);
    </script>
</body>
</html>
