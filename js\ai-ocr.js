/**
 * AI OCR识别模块
 * 支持多种AI识别服务：百度、腾讯云、Azure等
 */

class AIOCRManager {
    constructor() {
        this.apiConfigs = {
            baidu: {
                apiKey: '',
                secretKey: '',
                accessToken: null,
                tokenExpiry: null
            },
            tencent: {
                secretId: '',
                secretKey: ''
            },
            azure: {
                apiKey: '',
                endpoint: ''
            }
        };
        
        this.loadApiConfigs();
    }

    /**
     * 加载保存的API配置
     */
    loadApiConfigs() {
        try {
            const saved = localStorage.getItem('aiOcrConfigs');
            if (saved) {
                this.apiConfigs = { ...this.apiConfigs, ...JSON.parse(saved) };
            }
        } catch (error) {
            console.error('加载API配置失败:', error);
        }
    }

    /**
     * 保存API配置
     */
    saveApiConfigs() {
        try {
            localStorage.setItem('aiOcrConfigs', JSON.stringify(this.apiConfigs));
        } catch (error) {
            console.error('保存API配置失败:', error);
        }
    }

    /**
     * 更新API配置
     */
    updateApiConfig(engine, config) {
        this.apiConfigs[engine] = { ...this.apiConfigs[engine], ...config };
        this.saveApiConfigs();
    }

    /**
     * 百度OCR识别
     */
    async recognizeWithBaidu(imageBase64) {
        try {
            // 模拟AI识别结果（演示用）
            if (this.apiConfigs.baidu.apiKey === 'demo') {
                return this.simulateAIRecognition(imageBase64);
            }

            // 获取访问令牌
            const accessToken = await this.getBaiduAccessToken();

            // 准备请求数据
            const imageData = imageBase64.split(',')[1]; // 移除data:image/...;base64,前缀

            // 使用通用文字识别（高精度版）API
            const formData = new FormData();
            formData.append('image', imageData);
            formData.append('recognize_granularity', 'big');
            formData.append('probability', 'true');
            formData.append('accuracy', 'high');

            // 智能选择调用方式
            let response;

            // 首先检查本地代理服务器是否可用
            const hasLocalProxy = await this.checkLocalProxy();

            if (hasLocalProxy) {
                console.log('使用本地代理服务器');
                response = await this.callBaiduAPIWithLocalProxy(accessToken, imageData);
            } else {
                try {
                    // 尝试直接调用（可能会因CORS失败）
                    console.log('尝试直接调用百度API');
                    response = await this.callBaiduAPIDirectly(accessToken, imageData);
                } catch (corsError) {
                    // 如果直接调用失败，提供解决方案
                    console.error('直接调用失败:', corsError.message);
                    throw new Error(`无法连接到百度API服务。请尝试以下解决方案：
1. 启动本地代理服务器（运行 node baidu-ocr-proxy.js）
2. 或者使用浏览器扩展禁用CORS检查
3. 或者使用演示模式（API Key输入"demo"）

错误详情: ${corsError.message}`);
                }
            }

            const result = await response.json();

            if (result.error_code) {
                throw new Error(`百度OCR错误 (${result.error_code}): ${result.error_msg}`);
            }

            return this.parseBaiduResult(result);
        } catch (error) {
            console.error('百度OCR识别失败:', error);
            throw error;
        }
    }

    /**
     * 检查本地代理服务器是否可用
     */
    async checkLocalProxy() {
        try {
            const response = await fetch('http://localhost:3001/health', {
                method: 'GET',
                timeout: 2000
            });
            return response.ok;
        } catch (error) {
            return false;
        }
    }

    /**
     * 使用本地代理调用百度API
     */
    async callBaiduAPIWithLocalProxy(accessToken, imageData) {
        const response = await fetch('http://localhost:3001/api/baidu/handwriting', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                accessToken: accessToken,
                image: imageData,
                options: {
                    granularity: 'big',
                    probability: true
                }
            })
        });

        if (!response.ok) {
            throw new Error(`代理服务器错误: ${response.statusText}`);
        }

        return response;
    }

    /**
     * 直接调用百度API（可能因CORS失败）
     */
    async callBaiduAPIDirectly(accessToken, imageData) {
        const formData = new URLSearchParams();
        formData.append('image', imageData);
        formData.append('recognize_granularity', 'big');

        const url = `https://aip.baidubce.com/rest/2.0/ocr/v1/handwriting?access_token=${accessToken}`;

        return await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: formData,
            mode: 'cors'
        });
    }

    /**
     * 获取百度访问令牌
     */
    async getBaiduAccessToken() {
        const config = this.apiConfigs.baidu;

        // 检查令牌是否有效
        if (config.accessToken && config.tokenExpiry && Date.now() < config.tokenExpiry) {
            return config.accessToken;
        }

        try {
            let response;

            // 检查本地代理服务器是否可用
            const hasLocalProxy = await this.checkLocalProxy();

            if (hasLocalProxy) {
                console.log('使用本地代理获取访问令牌');
                response = await fetch('http://localhost:3001/api/baidu/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        apiKey: config.apiKey,
                        secretKey: config.secretKey
                    })
                });
            } else {
                // 尝试直接获取令牌
                console.log('尝试直接获取访问令牌');
                response = await fetch('https://aip.baidubce.com/oauth/2.0/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: new URLSearchParams({
                        grant_type: 'client_credentials',
                        client_id: config.apiKey,
                        client_secret: config.secretKey
                    }),
                    mode: 'cors'
                });
            }

            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();

            if (result.error) {
                throw new Error(`百度API错误: ${result.error_description || result.error}`);
            }

            if (!result.access_token) {
                throw new Error('未能获取到访问令牌，请检查API密钥是否正确');
            }

            // 保存令牌
            config.accessToken = result.access_token;
            config.tokenExpiry = Date.now() + (result.expires_in - 300) * 1000; // 提前5分钟过期
            this.saveApiConfigs();

            console.log('成功获取百度访问令牌');
            return config.accessToken;
        } catch (error) {
            console.error('获取百度访问令牌失败:', error);

            // 提供详细的错误信息和解决方案
            let errorMessage = '获取访问令牌失败。';

            if (error.message.includes('CORS')) {
                errorMessage += '\n\n解决方案：\n1. 启动本地代理服务器（运行 node baidu-ocr-proxy.js）\n2. 或者使用演示模式（API Key输入"demo"）';
            } else if (error.message.includes('百度API错误')) {
                errorMessage += '\n\n请检查：\n1. API Key是否正确\n2. Secret Key是否正确\n3. 账号是否已开通OCR服务';
            } else {
                errorMessage += `\n\n错误详情: ${error.message}`;
            }

            throw new Error(errorMessage);
        }
    }

    /**
     * 解析百度OCR结果
     */
    parseBaiduResult(result) {
        const lines = [];
        
        if (result.words_result && result.words_result.length > 0) {
            for (const item of result.words_result) {
                if (item.words) {
                    lines.push(item.words);
                }
            }
        }

        return lines.join('\n');
    }

    /**
     * 腾讯云OCR识别
     */
    async recognizeWithTencent(imageBase64) {
        try {
            const config = this.apiConfigs.tencent;
            const timestamp = Math.floor(Date.now() / 1000);
            
            // 构建请求参数
            const params = {
                Action: 'GeneralHandwritingOCR',
                Version: '2018-11-19',
                Region: 'ap-beijing',
                ImageBase64: imageBase64.split(',')[1],
                Timestamp: timestamp,
                Nonce: Math.floor(Math.random() * 1000000)
            };

            // 生成签名
            const signature = await this.generateTencentSignature(params, config.secretKey);
            
            // 发送请求
            const response = await fetch('https://ocr.tencentcloudapi.com/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `TC3-HMAC-SHA256 Credential=${config.secretId}/${this.getDate()}/${params.Region}/ocr/tc3_request, SignedHeaders=content-type;host, Signature=${signature}`,
                    'X-TC-Action': params.Action,
                    'X-TC-Version': params.Version,
                    'X-TC-Region': params.Region,
                    'X-TC-Timestamp': timestamp
                },
                body: JSON.stringify({
                    ImageBase64: params.ImageBase64
                })
            });

            const result = await response.json();
            
            if (result.Response.Error) {
                throw new Error(`腾讯云OCR错误: ${result.Response.Error.Message}`);
            }

            return this.parseTencentResult(result);
        } catch (error) {
            console.error('腾讯云OCR识别失败:', error);
            throw error;
        }
    }

    /**
     * 生成腾讯云签名
     */
    async generateTencentSignature(params, secretKey) {
        // 这里需要实现腾讯云的签名算法
        // 由于签名算法比较复杂，建议使用腾讯云SDK
        // 这里提供一个简化版本，实际使用时建议使用官方SDK
        return 'simplified_signature';
    }

    /**
     * 获取日期字符串
     */
    getDate() {
        return new Date().toISOString().split('T')[0];
    }

    /**
     * 解析腾讯云OCR结果
     */
    parseTencentResult(result) {
        const lines = [];
        
        if (result.Response.TextDetections && result.Response.TextDetections.length > 0) {
            for (const item of result.Response.TextDetections) {
                if (item.DetectedText) {
                    lines.push(item.DetectedText);
                }
            }
        }

        return lines.join('\n');
    }

    /**
     * Azure Computer Vision OCR识别
     */
    async recognizeWithAzure(imageBase64) {
        try {
            const config = this.apiConfigs.azure;
            
            // 将base64转换为blob
            const response = await fetch(imageBase64);
            const blob = await response.blob();

            // 调用Azure OCR API
            const ocrResponse = await fetch(`${config.endpoint}/vision/v3.2/read/analyze`, {
                method: 'POST',
                headers: {
                    'Ocp-Apim-Subscription-Key': config.apiKey,
                    'Content-Type': 'application/octet-stream'
                },
                body: blob
            });

            if (!ocrResponse.ok) {
                throw new Error(`Azure OCR请求失败: ${ocrResponse.statusText}`);
            }

            // 获取操作位置
            const operationLocation = ocrResponse.headers.get('Operation-Location');
            
            // 轮询结果
            return await this.pollAzureResult(operationLocation, config.apiKey);
        } catch (error) {
            console.error('Azure OCR识别失败:', error);
            throw error;
        }
    }

    /**
     * 轮询Azure OCR结果
     */
    async pollAzureResult(operationLocation, apiKey) {
        const maxAttempts = 10;
        const delay = 1000; // 1秒

        for (let i = 0; i < maxAttempts; i++) {
            const response = await fetch(operationLocation, {
                headers: {
                    'Ocp-Apim-Subscription-Key': apiKey
                }
            });

            const result = await response.json();

            if (result.status === 'succeeded') {
                return this.parseAzureResult(result);
            } else if (result.status === 'failed') {
                throw new Error('Azure OCR处理失败');
            }

            // 等待后重试
            await new Promise(resolve => setTimeout(resolve, delay));
        }

        throw new Error('Azure OCR处理超时');
    }

    /**
     * 解析Azure OCR结果
     */
    parseAzureResult(result) {
        const lines = [];
        
        if (result.analyzeResult && result.analyzeResult.readResults) {
            for (const page of result.analyzeResult.readResults) {
                if (page.lines) {
                    for (const line of page.lines) {
                        if (line.text) {
                            lines.push(line.text);
                        }
                    }
                }
            }
        }

        return lines.join('\n');
    }

    /**
     * 根据选择的引擎进行识别
     */
    async recognize(engine, imageBase64) {
        switch (engine) {
            case 'baidu':
                return await this.recognizeWithBaidu(imageBase64);
            case 'tencent':
                return await this.recognizeWithTencent(imageBase64);
            case 'azure':
                return await this.recognizeWithAzure(imageBase64);
            default:
                throw new Error(`不支持的识别引擎: ${engine}`);
        }
    }

    /**
     * 模拟AI识别（演示用）
     */
    async simulateAIRecognition(imageBase64) {
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 返回模拟的识别结果
        return `苹果 apple
香蕉 banana
橙子 orange
葡萄 grape
草莓 strawberry
西瓜 watermelon
桃子 peach
梨子 pear
樱桃 cherry
菠萝 pineapple`;
    }

    /**
     * 验证API配置
     */
    validateConfig(engine) {
        const config = this.apiConfigs[engine];

        switch (engine) {
            case 'baidu':
                return config.apiKey && config.secretKey;
            case 'tencent':
                return config.secretId && config.secretKey;
            case 'azure':
                return config.apiKey && config.endpoint;
            default:
                return false;
        }
    }
}

// 创建全局AI OCR管理器实例
window.aiOcrManager = new AIOCRManager();
