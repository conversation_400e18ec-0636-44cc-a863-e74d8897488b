/**
 * AI OCR识别模块
 * 支持多种AI识别服务：百度、腾讯云、Azure等
 */

class AIOCRManager {
    constructor() {
        this.apiConfigs = {
            baidu: {
                apiKey: '',
                secretKey: '',
                accessToken: null,
                tokenExpiry: null
            },
            tencent: {
                secretId: '',
                secretKey: ''
            },
            azure: {
                apiKey: '',
                endpoint: ''
            }
        };
        
        this.loadApiConfigs();
    }

    /**
     * 加载保存的API配置
     */
    loadApiConfigs() {
        try {
            const saved = localStorage.getItem('aiOcrConfigs');
            if (saved) {
                this.apiConfigs = { ...this.apiConfigs, ...JSON.parse(saved) };
            }
        } catch (error) {
            console.error('加载API配置失败:', error);
        }
    }

    /**
     * 保存API配置
     */
    saveApiConfigs() {
        try {
            localStorage.setItem('aiOcrConfigs', JSON.stringify(this.apiConfigs));
        } catch (error) {
            console.error('保存API配置失败:', error);
        }
    }

    /**
     * 更新API配置
     */
    updateApiConfig(engine, config) {
        this.apiConfigs[engine] = { ...this.apiConfigs[engine], ...config };
        this.saveApiConfigs();
    }

    /**
     * 百度OCR识别
     * 注意：由于CORS限制，直接调用百度API可能失败
     * 建议通过后端代理或使用百度SDK
     */
    async recognizeWithBaidu(imageBase64) {
        try {
            // 模拟AI识别结果（演示用）
            if (this.apiConfigs.baidu.apiKey === 'demo') {
                return this.simulateAIRecognition(imageBase64);
            }

            // 实际的百度OCR调用需要后端代理
            throw new Error('百度OCR需要后端代理服务，请参考文档配置后端API');

            // 以下是实际调用代码（需要后端代理）
            /*
            const accessToken = await this.getBaiduAccessToken();

            const response = await fetch('/api/baidu-ocr', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    access_token: accessToken,
                    image: imageBase64.split(',')[1],
                    recognize_granularity: 'big'
                })
            });

            const result = await response.json();

            if (result.error_code) {
                throw new Error(`百度OCR错误: ${result.error_msg}`);
            }

            return this.parseBaiduResult(result);
            */
        } catch (error) {
            console.error('百度OCR识别失败:', error);
            throw error;
        }
    }

    /**
     * 获取百度访问令牌
     */
    async getBaiduAccessToken() {
        const config = this.apiConfigs.baidu;
        
        // 检查令牌是否有效
        if (config.accessToken && config.tokenExpiry && Date.now() < config.tokenExpiry) {
            return config.accessToken;
        }

        // 获取新令牌
        const response = await fetch('https://aip.baidubce.com/oauth/2.0/token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: new URLSearchParams({
                grant_type: 'client_credentials',
                client_id: config.apiKey,
                client_secret: config.secretKey
            })
        });

        const result = await response.json();
        
        if (result.error) {
            throw new Error(`获取百度访问令牌失败: ${result.error_description}`);
        }

        // 保存令牌
        config.accessToken = result.access_token;
        config.tokenExpiry = Date.now() + (result.expires_in - 300) * 1000; // 提前5分钟过期
        this.saveApiConfigs();

        return config.accessToken;
    }

    /**
     * 解析百度OCR结果
     */
    parseBaiduResult(result) {
        const lines = [];
        
        if (result.words_result && result.words_result.length > 0) {
            for (const item of result.words_result) {
                if (item.words) {
                    lines.push(item.words);
                }
            }
        }

        return lines.join('\n');
    }

    /**
     * 腾讯云OCR识别
     */
    async recognizeWithTencent(imageBase64) {
        try {
            const config = this.apiConfigs.tencent;
            const timestamp = Math.floor(Date.now() / 1000);
            
            // 构建请求参数
            const params = {
                Action: 'GeneralHandwritingOCR',
                Version: '2018-11-19',
                Region: 'ap-beijing',
                ImageBase64: imageBase64.split(',')[1],
                Timestamp: timestamp,
                Nonce: Math.floor(Math.random() * 1000000)
            };

            // 生成签名
            const signature = await this.generateTencentSignature(params, config.secretKey);
            
            // 发送请求
            const response = await fetch('https://ocr.tencentcloudapi.com/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `TC3-HMAC-SHA256 Credential=${config.secretId}/${this.getDate()}/${params.Region}/ocr/tc3_request, SignedHeaders=content-type;host, Signature=${signature}`,
                    'X-TC-Action': params.Action,
                    'X-TC-Version': params.Version,
                    'X-TC-Region': params.Region,
                    'X-TC-Timestamp': timestamp
                },
                body: JSON.stringify({
                    ImageBase64: params.ImageBase64
                })
            });

            const result = await response.json();
            
            if (result.Response.Error) {
                throw new Error(`腾讯云OCR错误: ${result.Response.Error.Message}`);
            }

            return this.parseTencentResult(result);
        } catch (error) {
            console.error('腾讯云OCR识别失败:', error);
            throw error;
        }
    }

    /**
     * 生成腾讯云签名
     */
    async generateTencentSignature(params, secretKey) {
        // 这里需要实现腾讯云的签名算法
        // 由于签名算法比较复杂，建议使用腾讯云SDK
        // 这里提供一个简化版本，实际使用时建议使用官方SDK
        return 'simplified_signature';
    }

    /**
     * 获取日期字符串
     */
    getDate() {
        return new Date().toISOString().split('T')[0];
    }

    /**
     * 解析腾讯云OCR结果
     */
    parseTencentResult(result) {
        const lines = [];
        
        if (result.Response.TextDetections && result.Response.TextDetections.length > 0) {
            for (const item of result.Response.TextDetections) {
                if (item.DetectedText) {
                    lines.push(item.DetectedText);
                }
            }
        }

        return lines.join('\n');
    }

    /**
     * Azure Computer Vision OCR识别
     */
    async recognizeWithAzure(imageBase64) {
        try {
            const config = this.apiConfigs.azure;
            
            // 将base64转换为blob
            const response = await fetch(imageBase64);
            const blob = await response.blob();

            // 调用Azure OCR API
            const ocrResponse = await fetch(`${config.endpoint}/vision/v3.2/read/analyze`, {
                method: 'POST',
                headers: {
                    'Ocp-Apim-Subscription-Key': config.apiKey,
                    'Content-Type': 'application/octet-stream'
                },
                body: blob
            });

            if (!ocrResponse.ok) {
                throw new Error(`Azure OCR请求失败: ${ocrResponse.statusText}`);
            }

            // 获取操作位置
            const operationLocation = ocrResponse.headers.get('Operation-Location');
            
            // 轮询结果
            return await this.pollAzureResult(operationLocation, config.apiKey);
        } catch (error) {
            console.error('Azure OCR识别失败:', error);
            throw error;
        }
    }

    /**
     * 轮询Azure OCR结果
     */
    async pollAzureResult(operationLocation, apiKey) {
        const maxAttempts = 10;
        const delay = 1000; // 1秒

        for (let i = 0; i < maxAttempts; i++) {
            const response = await fetch(operationLocation, {
                headers: {
                    'Ocp-Apim-Subscription-Key': apiKey
                }
            });

            const result = await response.json();

            if (result.status === 'succeeded') {
                return this.parseAzureResult(result);
            } else if (result.status === 'failed') {
                throw new Error('Azure OCR处理失败');
            }

            // 等待后重试
            await new Promise(resolve => setTimeout(resolve, delay));
        }

        throw new Error('Azure OCR处理超时');
    }

    /**
     * 解析Azure OCR结果
     */
    parseAzureResult(result) {
        const lines = [];
        
        if (result.analyzeResult && result.analyzeResult.readResults) {
            for (const page of result.analyzeResult.readResults) {
                if (page.lines) {
                    for (const line of page.lines) {
                        if (line.text) {
                            lines.push(line.text);
                        }
                    }
                }
            }
        }

        return lines.join('\n');
    }

    /**
     * 根据选择的引擎进行识别
     */
    async recognize(engine, imageBase64) {
        switch (engine) {
            case 'baidu':
                return await this.recognizeWithBaidu(imageBase64);
            case 'tencent':
                return await this.recognizeWithTencent(imageBase64);
            case 'azure':
                return await this.recognizeWithAzure(imageBase64);
            default:
                throw new Error(`不支持的识别引擎: ${engine}`);
        }
    }

    /**
     * 模拟AI识别（演示用）
     */
    async simulateAIRecognition(imageBase64) {
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 返回模拟的识别结果
        return `苹果 apple
香蕉 banana
橙子 orange
葡萄 grape
草莓 strawberry
西瓜 watermelon
桃子 peach
梨子 pear
樱桃 cherry
菠萝 pineapple`;
    }

    /**
     * 验证API配置
     */
    validateConfig(engine) {
        const config = this.apiConfigs[engine];

        switch (engine) {
            case 'baidu':
                return config.apiKey && config.secretKey;
            case 'tencent':
                return config.secretId && config.secretKey;
            case 'azure':
                return config.apiKey && config.endpoint;
            default:
                return false;
        }
    }
}

// 创建全局AI OCR管理器实例
window.aiOcrManager = new AIOCRManager();
