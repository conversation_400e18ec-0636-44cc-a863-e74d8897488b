# DeepSeek API 422错误排查指南

## 🚨 问题描述

您遇到的422错误码通常表示"Unprocessable Entity"，即请求格式正确但内容有问题。

## 🔍 常见原因分析

### 1. API端点问题
**可能原因：**
- 使用了错误的API端点
- 缺少 `/v1` 路径

**解决方案：**
```
❌ 错误: https://api.deepseek.com/chat/completions
✅ 正确: https://api.deepseek.com/v1/chat/completions
```

### 2. 模型名称问题
**可能原因：**
- 模型名称不正确
- 账户没有权限访问该模型

**解决方案：**
```javascript
// 尝试不同的模型名称
"deepseek-vl-chat"     // 视觉模型
"deepseek-chat"        // 文本模型
"deepseek-coder"       // 代码模型
```

### 3. 请求格式问题
**可能原因：**
- 图片格式不正确
- 消息结构有误
- 参数类型错误

### 4. 账户权限问题
**可能原因：**
- API Key无效或过期
- 账户余额不足
- 没有开通视觉模型权限

## 🛠️ 排查步骤

### 步骤1: 测试API连接
1. 打开测试工具：`test_deepseek_api.html`
2. 输入您的API Key
3. 点击"测试连接"按钮
4. 查看连接状态

### 步骤2: 验证API Key
```bash
# 使用curl测试
curl -X GET "https://api.deepseek.com/v1/models" \
  -H "Authorization: Bearer YOUR_API_KEY"
```

### 步骤3: 测试文本对话
1. 在测试工具中点击"测试文本对话"
2. 确认基础API调用正常

### 步骤4: 测试视觉功能
1. 点击"测试视觉识别"
2. 上传图片或生成测试图片
3. 查看是否支持视觉模型

## 🔧 解决方案

### 方案1: 更新API配置
```javascript
// 确保使用正确的配置
const config = {
    apiKey: 'YOUR_API_KEY',
    baseUrl: 'https://api.deepseek.com/v1'  // 注意包含 /v1
};
```

### 方案2: 检查模型可用性
```javascript
// 先测试文本模型
const textRequest = {
    model: 'deepseek-chat',  // 使用基础文本模型
    messages: [{ role: 'user', content: 'Hello' }]
};

// 再测试视觉模型
const visionRequest = {
    model: 'deepseek-vl-chat',  // 视觉模型
    messages: [{ 
        role: 'user', 
        content: [
            { type: 'text', text: 'Describe this image' },
            { type: 'image_url', image_url: { url: 'data:image/...' } }
        ]
    }]
};
```

### 方案3: 优化图片处理
```javascript
// 确保图片格式正确
function optimizeImage(imageBase64) {
    // 检查图片大小
    if (imageBase64.length > 4 * 1024 * 1024) {  // 4MB限制
        throw new Error('图片过大，请压缩后重试');
    }
    
    // 确保是有效的base64格式
    if (!imageBase64.startsWith('data:image/')) {
        throw new Error('图片格式不正确');
    }
    
    return imageBase64;
}
```

## 📋 快速检查清单

### ✅ API配置检查
- [ ] API Key是否正确
- [ ] Base URL是否包含 `/v1`
- [ ] 账户余额是否充足
- [ ] 是否有模型访问权限

### ✅ 请求格式检查
- [ ] Content-Type: application/json
- [ ] Authorization: Bearer YOUR_API_KEY
- [ ] 模型名称是否正确
- [ ] 消息格式是否符合规范

### ✅ 图片格式检查
- [ ] 图片是否为base64格式
- [ ] 图片大小是否超限（建议<4MB）
- [ ] 图片格式是否支持（JPG/PNG）

## 🚀 推荐解决流程

### 1. 立即测试
```bash
# 访问测试工具
http://localhost:8000/test_deepseek_api.html
```

### 2. 逐步验证
1. **连接测试** → 确认API Key有效
2. **文本测试** → 确认基础功能正常
3. **视觉测试** → 确认视觉模型可用

### 3. 常见错误码含义
- **401**: API Key无效
- **403**: 权限不足
- **422**: 请求格式错误
- **429**: 请求频率超限
- **500**: 服务器错误

## 💡 替代方案

如果DeepSeek仍然有问题，可以尝试：

### 1. 使用其他LLM
- **OpenAI GPT-4 Vision** (最稳定)
- **Claude 3 Vision** (高质量)
- **Qwen-VL** (中文优化)

### 2. 降级到传统OCR
- 百度AI OCR (手写体效果好)
- Tesseract (免费本地)

### 3. 混合方案
```javascript
// 优先使用LLM，失败时降级
async function smartOCR(image) {
    try {
        return await llmOCR(image);  // 尝试LLM
    } catch (error) {
        console.warn('LLM失败，降级到传统OCR');
        return await traditionalOCR(image);  // 降级方案
    }
}
```

## 📞 获取帮助

### DeepSeek官方支持
- **文档**: https://platform.deepseek.com/api-docs/
- **社区**: https://github.com/deepseek-ai
- **邮箱**: <EMAIL>

### 本地调试
1. 打开浏览器开发者工具
2. 查看Network标签页
3. 检查API请求和响应详情
4. 复制错误信息进行分析

---

## 🎯 立即行动

1. **打开测试工具**: `http://localhost:8000/test_deepseek_api.html`
2. **输入您的API Key**
3. **逐步测试各项功能**
4. **根据测试结果调整配置**

如果问题仍然存在，请提供测试工具的详细错误信息，我将帮您进一步诊断！
