/**
 * 百度OCR代理服务器
 * 用于解决CORS跨域问题
 * 
 * 使用方法：
 * 1. 安装Node.js
 * 2. 运行: npm install express cors
 * 3. 运行: node baidu-ocr-proxy.js
 * 4. 代理服务器将在 http://localhost:3001 启动
 */

const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');

const app = express();
const PORT = 3001;

// 启用CORS
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 获取访问令牌的代理接口
app.post('/api/baidu/token', async (req, res) => {
    try {
        const { apiKey, secretKey } = req.body;
        
        const response = await fetch('https://aip.baidubce.com/oauth/2.0/token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: new URLSearchParams({
                grant_type: 'client_credentials',
                client_id: apiKey,
                client_secret: secretKey
            })
        });

        const result = await response.json();
        res.json(result);
    } catch (error) {
        console.error('获取百度令牌失败:', error);
        res.status(500).json({ error: error.message });
    }
});

// OCR识别的代理接口
app.post('/api/baidu/ocr', async (req, res) => {
    try {
        const { accessToken, image, options = {} } = req.body;
        
        const formData = new URLSearchParams();
        formData.append('image', image);
        formData.append('recognize_granularity', options.granularity || 'big');
        if (options.probability) formData.append('probability', 'true');
        if (options.accuracy) formData.append('accuracy', 'high');
        
        const response = await fetch(`https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic?access_token=${accessToken}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: formData
        });

        const result = await response.json();
        res.json(result);
    } catch (error) {
        console.error('百度OCR识别失败:', error);
        res.status(500).json({ error: error.message });
    }
});

// 手写文字识别的代理接口
app.post('/api/baidu/handwriting', async (req, res) => {
    try {
        const { accessToken, image, options = {} } = req.body;
        
        const formData = new URLSearchParams();
        formData.append('image', image);
        formData.append('recognize_granularity', options.granularity || 'big');
        
        const response = await fetch(`https://aip.baidubce.com/rest/2.0/ocr/v1/handwriting?access_token=${accessToken}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: formData
        });

        const result = await response.json();
        res.json(result);
    } catch (error) {
        console.error('百度手写识别失败:', error);
        res.status(500).json({ error: error.message });
    }
});

// 健康检查接口
app.get('/health', (req, res) => {
    res.json({ status: 'ok', message: '百度OCR代理服务运行正常' });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`百度OCR代理服务器已启动: http://localhost:${PORT}`);
    console.log('可用接口:');
    console.log('  POST /api/baidu/token - 获取访问令牌');
    console.log('  POST /api/baidu/ocr - 通用文字识别');
    console.log('  POST /api/baidu/handwriting - 手写文字识别');
    console.log('  GET /health - 健康检查');
});

module.exports = app;
