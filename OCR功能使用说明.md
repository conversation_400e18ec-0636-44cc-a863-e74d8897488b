# OCR图片识别功能使用说明

## 功能概述

新增的OCR（光学字符识别）功能可以帮助您从图片中识别手写或印刷的中英文文字，并自动将识别结果添加到词库中。这个功能特别适合：

- 从教科书、练习册中提取词汇
- 识别手写笔记中的单词
- 处理扫描文档中的词汇表
- 快速录入纸质材料中的词汇

## 使用步骤

### 1. 打开OCR功能
- 在词表管理区域，点击紫色的"图片识别"按钮
- 系统会自动打开文件选择对话框

### 2. 上传图片
- 选择包含中英文文字的图片文件
- 支持的格式：JPG, PNG, GIF, BMP等常见图片格式
- 建议图片大小不超过10MB
- 图片上传后会显示预览

### 3. 开始识别
- 点击"开始识别"按钮
- 首次使用时系统会下载OCR引擎（约几MB），请耐心等待
- 识别过程中会显示进度条

### 4. 查看和编辑结果
- **原始识别文本**：显示OCR识别的原始文字内容
- **词汇配对**：系统会自动尝试将中英文进行配对
- 您可以手动编辑配对结果，确保准确性

### 5. 添加到词表
- 检查配对结果无误后，点击"添加到词表"
- 识别的词汇会自动添加到当前编辑的词表中
- 可以继续编辑或保存词表

## 使用技巧

### 图片质量要求
- **清晰度**：确保文字清晰可见，避免模糊
- **对比度**：黑白对比度高的图片识别效果更好
- **角度**：尽量保持文字水平，避免倾斜
- **光线**：充足均匀的光线有助于提高识别准确率

### 文字布局建议
- **简单布局**：避免复杂的排版和装饰
- **字体大小**：字体不要太小，建议12号字以上
- **行间距**：适当的行间距有助于识别
- **背景**：纯色背景效果更好

### 智能配对功能
- 系统会自动识别中文和英文单词
- 按行进行配对，每行最好包含一对中英文
- 支持的格式：
  - `中文 英文`（空格分隔）
  - `中文,英文`（逗号分隔）
  - `中文	英文`（制表符分隔）

## 常见问题

### Q: 识别准确率不高怎么办？
A: 
- 检查图片质量，确保文字清晰
- 尝试调整图片亮度和对比度
- 手动编辑识别结果中的错误

### Q: 支持哪些语言？
A: 目前支持中文（简体）和英文的混合识别

### Q: 识别速度很慢？
A: 
- 首次使用需要下载语言包，会比较慢
- 图片过大会影响识别速度，建议压缩图片
- 复杂的图片需要更多处理时间

### Q: 无法识别手写文字？
A: 
- 手写文字识别准确率相对较低
- 建议使用工整的手写字体
- 印刷体文字识别效果更好

## 技术说明

- 使用Tesseract.js OCR引擎
- 支持客户端处理，保护隐私
- 支持中英文混合识别
- 自动智能配对算法

## 注意事项

1. **隐私保护**：所有图片处理都在本地进行，不会上传到服务器
2. **浏览器兼容性**：建议使用现代浏览器（Chrome、Firefox、Safari、Edge）
3. **性能要求**：OCR处理需要一定的计算资源，低配置设备可能较慢
4. **网络要求**：首次使用需要下载OCR引擎文件

## 更新日志

### v1.0.0
- 新增OCR图片识别功能
- 支持中英文混合识别
- 智能词汇配对算法
- 集成到词表管理系统
