/**
 * 大语言模型OCR识别模块
 * 支持DeepSeek、OpenAI GPT-4 Vision、Claude 3 Vision、Qwen-VL等多模态模型
 */

class LLMOCRManager {
    constructor() {
        this.apiConfigs = {
            deepseek: {
                apiKey: '',
                baseUrl: 'https://api.deepseek.com'
            },
            openai: {
                apiKey: '',
                baseUrl: 'https://api.openai.com/v1'
            },
            claude: {
                apiKey: '',
                baseUrl: 'https://api.anthropic.com'
            },
            qwen: {
                apiKey: '',
                baseUrl: 'https://dashscope.aliyuncs.com/api/v1'
            }
        };
        
        this.loadApiConfigs();
    }

    /**
     * 加载保存的API配置
     */
    loadApiConfigs() {
        try {
            const saved = localStorage.getItem('llmOcrConfigs');
            if (saved) {
                this.apiConfigs = { ...this.apiConfigs, ...JSON.parse(saved) };
            }
        } catch (error) {
            console.error('加载LLM API配置失败:', error);
        }
    }

    /**
     * 保存API配置
     */
    saveApiConfigs() {
        try {
            localStorage.setItem('llmOcrConfigs', JSON.stringify(this.apiConfigs));
        } catch (error) {
            console.error('保存LLM API配置失败:', error);
        }
    }

    /**
     * 更新API配置
     */
    updateApiConfig(engine, config) {
        this.apiConfigs[engine] = { ...this.apiConfigs[engine], ...config };
        this.saveApiConfigs();
    }

    /**
     * 生成OCR提示词
     */
    generateOCRPrompt() {
        return `请仔细识别图片中的所有文字内容，包括中文和英文。

要求：
1. 准确识别图片中的所有文字，包括手写体和印刷体
2. 保持原有的文字布局和换行
3. 如果是中英文对照的词汇表，请按照原有格式输出
4. 不要添加任何解释或说明，只输出识别到的文字内容
5. 如果文字不清楚，请尽力识别，不要跳过

请直接输出识别结果：`;
    }

    /**
     * DeepSeek Vision识别
     */
    async recognizeWithDeepSeek(imageBase64) {
        try {
            const config = this.apiConfigs.deepseek;
            
            // 演示模式
            if (config.apiKey === 'demo') {
                return this.simulateAIRecognition();
            }

            const response = await fetch(`${config.baseUrl}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${config.apiKey}`
                },
                body: JSON.stringify({
                    model: 'deepseek-vl-chat',
                    messages: [
                        {
                            role: 'user',
                            content: [
                                {
                                    type: 'text',
                                    text: this.generateOCRPrompt()
                                },
                                {
                                    type: 'image_url',
                                    image_url: {
                                        url: imageBase64
                                    }
                                }
                            ]
                        }
                    ],
                    max_tokens: 2000,
                    temperature: 0.1
                })
            });

            if (!response.ok) {
                throw new Error(`DeepSeek API错误: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();
            
            if (result.error) {
                throw new Error(`DeepSeek错误: ${result.error.message}`);
            }

            return result.choices[0].message.content.trim();
        } catch (error) {
            console.error('DeepSeek识别失败:', error);
            throw error;
        }
    }

    /**
     * OpenAI GPT-4 Vision识别
     */
    async recognizeWithOpenAI(imageBase64) {
        try {
            const config = this.apiConfigs.openai;
            
            // 演示模式
            if (config.apiKey === 'demo') {
                return this.simulateAIRecognition();
            }

            const response = await fetch(`${config.baseUrl}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${config.apiKey}`
                },
                body: JSON.stringify({
                    model: 'gpt-4-vision-preview',
                    messages: [
                        {
                            role: 'user',
                            content: [
                                {
                                    type: 'text',
                                    text: this.generateOCRPrompt()
                                },
                                {
                                    type: 'image_url',
                                    image_url: {
                                        url: imageBase64,
                                        detail: 'high'
                                    }
                                }
                            ]
                        }
                    ],
                    max_tokens: 2000,
                    temperature: 0.1
                })
            });

            if (!response.ok) {
                throw new Error(`OpenAI API错误: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();
            
            if (result.error) {
                throw new Error(`OpenAI错误: ${result.error.message}`);
            }

            return result.choices[0].message.content.trim();
        } catch (error) {
            console.error('OpenAI识别失败:', error);
            throw error;
        }
    }

    /**
     * Claude 3 Vision识别
     */
    async recognizeWithClaude(imageBase64) {
        try {
            const config = this.apiConfigs.claude;
            
            // 演示模式
            if (config.apiKey === 'demo') {
                return this.simulateAIRecognition();
            }

            // 提取base64数据和媒体类型
            const [header, data] = imageBase64.split(',');
            const mediaType = header.match(/data:([^;]+)/)[1];

            const response = await fetch(`${config.baseUrl}/v1/messages`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'x-api-key': config.apiKey,
                    'anthropic-version': '2023-06-01'
                },
                body: JSON.stringify({
                    model: 'claude-3-sonnet-20240229',
                    max_tokens: 2000,
                    temperature: 0.1,
                    messages: [
                        {
                            role: 'user',
                            content: [
                                {
                                    type: 'text',
                                    text: this.generateOCRPrompt()
                                },
                                {
                                    type: 'image',
                                    source: {
                                        type: 'base64',
                                        media_type: mediaType,
                                        data: data
                                    }
                                }
                            ]
                        }
                    ]
                })
            });

            if (!response.ok) {
                throw new Error(`Claude API错误: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();
            
            if (result.error) {
                throw new Error(`Claude错误: ${result.error.message}`);
            }

            return result.content[0].text.trim();
        } catch (error) {
            console.error('Claude识别失败:', error);
            throw error;
        }
    }

    /**
     * Qwen-VL识别
     */
    async recognizeWithQwen(imageBase64) {
        try {
            const config = this.apiConfigs.qwen;
            
            // 演示模式
            if (config.apiKey === 'demo') {
                return this.simulateAIRecognition();
            }

            const response = await fetch(`${config.baseUrl}/services/aigc/multimodal-generation/generation`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${config.apiKey}`
                },
                body: JSON.stringify({
                    model: 'qwen-vl-plus',
                    input: {
                        messages: [
                            {
                                role: 'user',
                                content: [
                                    {
                                        text: this.generateOCRPrompt()
                                    },
                                    {
                                        image: imageBase64
                                    }
                                ]
                            }
                        ]
                    },
                    parameters: {
                        max_tokens: 2000,
                        temperature: 0.1
                    }
                })
            });

            if (!response.ok) {
                throw new Error(`Qwen API错误: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();
            
            if (result.code && result.code !== '200') {
                throw new Error(`Qwen错误: ${result.message}`);
            }

            return result.output.choices[0].message.content[0].text.trim();
        } catch (error) {
            console.error('Qwen识别失败:', error);
            throw error;
        }
    }

    /**
     * 根据选择的引擎进行识别
     */
    async recognize(engine, imageBase64) {
        switch (engine) {
            case 'deepseek':
                return await this.recognizeWithDeepSeek(imageBase64);
            case 'openai':
                return await this.recognizeWithOpenAI(imageBase64);
            case 'claude':
                return await this.recognizeWithClaude(imageBase64);
            case 'qwen':
                return await this.recognizeWithQwen(imageBase64);
            default:
                throw new Error(`不支持的LLM引擎: ${engine}`);
        }
    }

    /**
     * 模拟AI识别（演示用）
     */
    async simulateAIRecognition() {
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 返回模拟的高质量识别结果
        return `苹果 apple
香蕉 banana  
橙子 orange
葡萄 grape
草莓 strawberry
西瓜 watermelon
桃子 peach
梨子 pear
樱桃 cherry
菠萝 pineapple
柠檬 lemon
芒果 mango`;
    }

    /**
     * 验证API配置
     */
    validateConfig(engine) {
        const config = this.apiConfigs[engine];
        return config && config.apiKey && config.apiKey.trim() !== '';
    }

    /**
     * 获取引擎显示名称
     */
    getEngineDisplayName(engine) {
        const names = {
            'deepseek': 'DeepSeek Vision',
            'openai': 'GPT-4 Vision',
            'claude': 'Claude 3 Vision',
            'qwen': 'Qwen-VL'
        };
        return names[engine] || engine;
    }
}

// 创建全局LLM OCR管理器实例
window.llmOcrManager = new LLMOCRManager();
