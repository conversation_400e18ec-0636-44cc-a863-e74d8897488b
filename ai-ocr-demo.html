<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI OCR识别演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .engine-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            font-size: 0.6rem;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .ocr-engine-card {
            transition: all 0.3s ease;
        }
        
        .ocr-engine-card:hover {
            border-color: #a855f7;
            background-color: #faf5ff;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(168, 85, 247, 0.1);
        }
        
        .comparison-table th {
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
        }
        
        .feature-excellent { color: #059669; font-weight: 600; }
        .feature-good { color: #0891b2; font-weight: 600; }
        .feature-average { color: #d97706; font-weight: 600; }
        .feature-poor { color: #dc2626; font-weight: 600; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <h1 class="text-2xl font-bold flex items-center">
                    <i class="fas fa-robot mr-3"></i>
                    AI OCR识别演示
                </h1>
                <a href="index.html" class="hover:bg-white hover:bg-opacity-20 px-3 py-2 rounded-lg transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>返回主应用
                </a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8 max-w-6xl">
        <!-- 功能介绍 -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">
                <i class="fas fa-magic mr-3 text-purple-600"></i>
                AI识别 vs 传统OCR
            </h2>
            <p class="text-gray-600 mb-6">
                新版本支持多种AI识别引擎，相比传统OCR在手写体识别方面有显著提升。
                以下是各种引擎的对比和演示。
            </p>
            
            <!-- 引擎对比表 -->
            <div class="overflow-x-auto">
                <table class="comparison-table w-full border-collapse border border-gray-300 rounded-lg overflow-hidden">
                    <thead>
                        <tr>
                            <th class="border border-gray-300 px-4 py-3 text-left">识别引擎</th>
                            <th class="border border-gray-300 px-4 py-3 text-center">印刷体</th>
                            <th class="border border-gray-300 px-4 py-3 text-center">手写体</th>
                            <th class="border border-gray-300 px-4 py-3 text-center">中文支持</th>
                            <th class="border border-gray-300 px-4 py-3 text-center">成本</th>
                            <th class="border border-gray-300 px-4 py-3 text-center">隐私</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white">
                        <tr>
                            <td class="border border-gray-300 px-4 py-3 font-medium">Tesseract (本地)</td>
                            <td class="border border-gray-300 px-4 py-3 text-center feature-good">优秀</td>
                            <td class="border border-gray-300 px-4 py-3 text-center feature-poor">较差</td>
                            <td class="border border-gray-300 px-4 py-3 text-center feature-good">良好</td>
                            <td class="border border-gray-300 px-4 py-3 text-center feature-excellent">免费</td>
                            <td class="border border-gray-300 px-4 py-3 text-center feature-excellent">最佳</td>
                        </tr>
                        <tr class="bg-purple-50">
                            <td class="border border-gray-300 px-4 py-3 font-medium">
                                百度AI OCR 
                                <span class="inline-block ml-2 px-2 py-1 bg-orange-500 text-white text-xs rounded">推荐</span>
                            </td>
                            <td class="border border-gray-300 px-4 py-3 text-center feature-excellent">优秀</td>
                            <td class="border border-gray-300 px-4 py-3 text-center feature-excellent">优秀</td>
                            <td class="border border-gray-300 px-4 py-3 text-center feature-excellent">优秀</td>
                            <td class="border border-gray-300 px-4 py-3 text-center feature-good">低成本</td>
                            <td class="border border-gray-300 px-4 py-3 text-center feature-average">一般</td>
                        </tr>
                        <tr>
                            <td class="border border-gray-300 px-4 py-3 font-medium">腾讯云OCR</td>
                            <td class="border border-gray-300 px-4 py-3 text-center feature-excellent">优秀</td>
                            <td class="border border-gray-300 px-4 py-3 text-center feature-good">良好</td>
                            <td class="border border-gray-300 px-4 py-3 text-center feature-good">良好</td>
                            <td class="border border-gray-300 px-4 py-3 text-center feature-good">低成本</td>
                            <td class="border border-gray-300 px-4 py-3 text-center feature-average">一般</td>
                        </tr>
                        <tr>
                            <td class="border border-gray-300 px-4 py-3 font-medium">Azure Vision</td>
                            <td class="border border-gray-300 px-4 py-3 text-center feature-excellent">优秀</td>
                            <td class="border border-gray-300 px-4 py-3 text-center feature-good">良好</td>
                            <td class="border border-gray-300 px-4 py-3 text-center feature-average">一般</td>
                            <td class="border border-gray-300 px-4 py-3 text-center feature-average">中等</td>
                            <td class="border border-gray-300 px-4 py-3 text-center feature-average">一般</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 快速演示 -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">
                <i class="fas fa-play-circle mr-3 text-green-600"></i>
                快速演示
            </h2>
            
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <div class="flex items-center">
                    <i class="fas fa-lightbulb text-yellow-600 mr-2"></i>
                    <span class="font-medium text-yellow-800">演示说明</span>
                </div>
                <p class="text-yellow-700 text-sm mt-2">
                    为了让您快速体验AI识别效果，我们提供了演示模式。
                    选择百度AI引擎，输入API Key和Secret Key都为"demo"即可体验。
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 演示步骤 -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">演示步骤：</h3>
                    <ol class="space-y-3">
                        <li class="flex items-start">
                            <span class="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</span>
                            <span>点击下方"开始演示"按钮</span>
                        </li>
                        <li class="flex items-start">
                            <span class="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</span>
                            <span>选择"百度AI"识别引擎</span>
                        </li>
                        <li class="flex items-start">
                            <span class="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</span>
                            <span>API Key和Secret Key都输入"demo"</span>
                        </li>
                        <li class="flex items-start">
                            <span class="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">4</span>
                            <span>上传包含中英文的图片</span>
                        </li>
                        <li class="flex items-start">
                            <span class="bg-purple-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">5</span>
                            <span>点击"开始识别"体验AI效果</span>
                        </li>
                    </ol>
                </div>

                <!-- 示例图片 -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">测试图片示例：</h3>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                        <canvas id="sampleCanvas" width="300" height="200" class="border rounded mb-3 mx-auto"></canvas>
                        <button id="generateSample" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                            <i class="fas fa-image mr-2"></i>生成测试图片
                        </button>
                        <button id="downloadSample" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm ml-2">
                            <i class="fas fa-download mr-2"></i>下载图片
                        </button>
                    </div>
                </div>
            </div>

            <div class="text-center mt-6">
                <a href="index.html" class="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-all transform hover:scale-105">
                    <i class="fas fa-rocket mr-2"></i>开始演示
                </a>
            </div>
        </div>

        <!-- 配置指南 -->
        <div class="bg-white rounded-xl shadow-lg p-6">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">
                <i class="fas fa-cog mr-3 text-blue-600"></i>
                配置指南
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- 百度AI -->
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-brain text-red-500 text-xl mr-2"></i>
                        <h3 class="font-semibold">百度AI OCR</h3>
                        <span class="ml-auto bg-orange-500 text-white text-xs px-2 py-1 rounded">推荐</span>
                    </div>
                    <p class="text-sm text-gray-600 mb-3">手写体识别效果最佳，中文支持优秀</p>
                    <ul class="text-xs text-gray-500 space-y-1 mb-4">
                        <li>• 免费额度：1000次/月</li>
                        <li>• 手写体识别准确率高</li>
                        <li>• 中文优化</li>
                    </ul>
                    <a href="https://cloud.baidu.com/product/ocr" target="_blank" class="text-blue-500 hover:underline text-sm">
                        <i class="fas fa-external-link-alt mr-1"></i>获取API密钥
                    </a>
                </div>

                <!-- 腾讯云 -->
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-cloud text-blue-500 text-xl mr-2"></i>
                        <h3 class="font-semibold">腾讯云OCR</h3>
                    </div>
                    <p class="text-sm text-gray-600 mb-3">性价比高，企业级稳定性</p>
                    <ul class="text-xs text-gray-500 space-y-1 mb-4">
                        <li>• 免费额度：1000次/月</li>
                        <li>• 企业级服务</li>
                        <li>• 多种识别类型</li>
                    </ul>
                    <a href="https://cloud.tencent.com/product/ocr" target="_blank" class="text-blue-500 hover:underline text-sm">
                        <i class="fas fa-external-link-alt mr-1"></i>获取API密钥
                    </a>
                </div>

                <!-- Azure -->
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-microsoft text-blue-600 text-xl mr-2"></i>
                        <h3 class="font-semibold">Azure Vision</h3>
                    </div>
                    <p class="text-sm text-gray-600 mb-3">微软AI服务，技术先进</p>
                    <ul class="text-xs text-gray-500 space-y-1 mb-4">
                        <li>• 免费额度：20000次/月</li>
                        <li>• 多语言支持</li>
                        <li>• 全球服务</li>
                    </ul>
                    <a href="https://azure.microsoft.com/services/cognitive-services/computer-vision/" target="_blank" class="text-blue-500 hover:underline text-sm">
                        <i class="fas fa-external-link-alt mr-1"></i>获取API密钥
                    </a>
                </div>
            </div>

            <div class="mt-6 text-center">
                <a href="AI识别配置指南.md" target="_blank" class="text-purple-600 hover:text-purple-800 font-medium">
                    <i class="fas fa-book mr-2"></i>查看详细配置指南
                </a>
            </div>
        </div>
    </div>

    <script>
        // 生成示例图片
        function generateSampleImage() {
            const canvas = document.getElementById('sampleCanvas');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 设置字体和样式
            ctx.fillStyle = 'black';
            ctx.font = '18px Arial';
            
            // 绘制中英文词汇
            const words = [
                '苹果 apple',
                '香蕉 banana',
                '橙子 orange',
                '葡萄 grape',
                '草莓 strawberry'
            ];
            
            words.forEach((word, index) => {
                ctx.fillText(word, 20, 30 + index * 35);
            });
        }

        // 下载示例图片
        function downloadSampleImage() {
            const canvas = document.getElementById('sampleCanvas');
            const link = document.createElement('a');
            link.download = 'sample-words.png';
            link.href = canvas.toDataURL();
            link.click();
        }

        // 事件监听
        document.getElementById('generateSample').addEventListener('click', generateSampleImage);
        document.getElementById('downloadSample').addEventListener('click', downloadSampleImage);

        // 页面加载时生成示例图片
        window.addEventListener('load', generateSampleImage);
    </script>
</body>
</html>
