/**
 * 自定义样式文件
 * 补充Tailwind CSS未覆盖的样式
 */

/* 全局样式 */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #374151;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* 工具类 */
.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

.bounce {
    animation: bounce 1s ease-in-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* 按钮样式增强 */
.btn-primary {
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

/* 模式卡片样式 */
.mode-card {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.mode-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.2));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.mode-card:hover::before {
    opacity: 1;
}

/* 选择题选项样式 */
.choice-option {
    position: relative;
    transition: all 0.2s ease;
}

.choice-option:hover {
    transform: translateX(4px);
}

.choice-option.selected {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border-color: #1d4ed8;
}

.choice-option.correct {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border-color: #059669;
}

.choice-option.wrong {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border-color: #dc2626;
}

/* 答题结果样式增强 */
.result-success {
    animation: successPulse 0.6s ease-out;
}

.result-error {
    animation: errorShake 0.6s ease-out;
}

@keyframes successPulse {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes errorShake {
    0%, 100% {
        transform: translateX(0);
        opacity: 0;
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
    100% {
        opacity: 1;
    }
}

/* 单词显示样式 */
.word-display {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid #cbd5e1;
    transition: all 0.3s ease;
}

.word-display:hover {
    border-color: #94a3b8;
    transform: translateY(-1px);
}

/* 斯宾塞复习模式样式 */
.spencer-info {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border: 2px solid #93c5fd;
    animation: fadeInUp 0.5s ease-out;
}

.mastery-level-0 { background: linear-gradient(135deg, #fee2e2, #fecaca); }
.mastery-level-1 { background: linear-gradient(135deg, #fed7aa, #fdba74); }
.mastery-level-2 { background: linear-gradient(135deg, #fef3c7, #fde68a); }
.mastery-level-3 { background: linear-gradient(135deg, #d1fae5, #a7f3d0); }
.mastery-level-4 { background: linear-gradient(135deg, #bfdbfe, #93c5fd); }
.mastery-level-5 { background: linear-gradient(135deg, #c7d2fe, #a5b4fc); }

.spencer-stats-modal {
    backdrop-filter: blur(8px);
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 掌握程度指示器 */
.mastery-indicator {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.mastery-0 { background: #fee2e2; color: #dc2626; }
.mastery-1 { background: #fed7aa; color: #ea580c; }
.mastery-2 { background: #fef3c7; color: #d97706; }
.mastery-3 { background: #d1fae5; color: #059669; }
.mastery-4 { background: #bfdbfe; color: #2563eb; }
.mastery-5 { background: #c7d2fe; color: #4f46e5; }

/* 间隔重复进度条 */
.interval-progress {
    height: 4px;
    background: linear-gradient(90deg, #ef4444, #f97316, #eab308, #22c55e, #3b82f6, #6366f1);
    border-radius: 2px;
    transition: width 0.3s ease;
}

/* 复习选择界面样式 */
.review-selection-container {
    max-height: 80vh;
    overflow-y: auto;
}

.filter-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #cbd5e1;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.filter-section h3 {
    color: #374151;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

/* 复习模式选择卡片 */
.review-mode-card {
    transition: all 0.2s ease;
    cursor: pointer;
}

.review-mode-card:hover {
    border-color: #a855f7;
    background-color: #faf5ff;
}

.review-mode-card input:checked + div {
    color: #7c3aed;
}

.review-mode-card:has(input:checked) {
    border-color: #7c3aed;
    background-color: #f3e8ff;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
}

/* 筛选条件复选框样式 */
.filter-checkbox {
    accent-color: #7c3aed;
}

.filter-checkbox:checked + span {
    font-weight: 600;
}

/* 预览统计卡片 */
.review-preview {
    background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
    border: 2px solid #c4b5fd;
    animation: previewPulse 2s infinite;
}

@keyframes previewPulse {
    0%, 100% {
        border-color: #c4b5fd;
    }
    50% {
        border-color: #a855f7;
    }
}

/* 单词预览列表样式 */
.word-preview-item {
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
}

.word-preview-item:hover {
    border-color: #a855f7;
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(168, 85, 247, 0.1);
}

/* 选中状态的单词项 */
.word-preview-item.selected {
    background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
    border-color: #a855f7;
    box-shadow: 0 0 0 1px #a855f7;
}

.word-preview-item.selected:hover {
    transform: translateX(0);
    box-shadow: 0 4px 12px rgba(168, 85, 247, 0.2);
}

/* 单词选择复选框样式 */
.word-checkbox {
    width: 18px;
    height: 18px;
    accent-color: #7c3aed;
    cursor: pointer;
}

.word-checkbox:checked {
    background-color: #7c3aed;
    border-color: #7c3aed;
}

/* 全选控制区域 */
.select-all-controls {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #cbd5e1;
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 0.75rem;
}

/* 选择计数器 */
.selection-counter {
    background: #7c3aed;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    animation: counterPulse 0.3s ease-out;
}

@keyframes counterPulse {
    0% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

/* 清空按钮样式 */
.clear-selection-btn {
    transition: all 0.2s ease;
    text-decoration: underline;
    text-decoration-color: transparent;
}

.clear-selection-btn:hover {
    text-decoration-color: currentColor;
    transform: translateY(-1px);
}

.word-preview-content {
    scrollbar-width: thin;
    scrollbar-color: #c4b5fd #f3e8ff;
}

.word-preview-content::-webkit-scrollbar {
    width: 6px;
}

.word-preview-content::-webkit-scrollbar-track {
    background: #f3e8ff;
    border-radius: 3px;
}

.word-preview-content::-webkit-scrollbar-thumb {
    background: #c4b5fd;
    border-radius: 3px;
}

.word-preview-content::-webkit-scrollbar-thumb:hover {
    background: #a855f7;
}

/* 单词状态标签 */
.word-status-tag {
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-overdue {
    background: #fef3c7;
    color: #d97706;
}

.status-learning {
    background: #d1fae5;
    color: #059669;
}

.status-error-count {
    background: #fee2e2;
    color: #dc2626;
}

/* 展开按钮样式 */
.toggle-preview-btn {
    transition: all 0.2s ease;
    position: relative;
}

.toggle-preview-btn:hover {
    color: #7c3aed;
    transform: translateY(-1px);
}

.toggle-preview-btn::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 1px;
    background: currentColor;
    transform: scaleX(0);
    transition: transform 0.2s ease;
}

.toggle-preview-btn:hover::after {
    transform: scaleX(1);
}

/* 掌握程度标签在复习选择中的样式 */
.mastery-indicator {
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 8px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: inline-block;
    min-width: 50px;
    text-align: center;
}

/* 筛选组样式 */
.filter-group {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    align-items: center;
}

.filter-group label {
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.filter-group label:hover {
    border-color: #7c3aed;
    background-color: #faf5ff;
}

.filter-group label:has(input:checked) {
    border-color: #7c3aed;
    background-color: #f3e8ff;
    color: #7c3aed;
    font-weight: 600;
}

.filter-group input[type="checkbox"] {
    margin-right: 0.5rem;
    accent-color: #7c3aed;
}

/* 数量选择器样式 */
.review-limit-select {
    background: white;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.review-limit-select:focus {
    border-color: #7c3aed;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
    outline: none;
}

/* 快速选择预设按钮 */
.preset-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.preset-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.preset-btn:active {
    transform: translateY(0);
}

.preset-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.preset-btn:hover::before {
    left: 100%;
}

/* 预设按钮特定颜色的环形效果 */
.preset-btn[data-preset="difficult"] {
    --ring-color: #ef4444;
}

.preset-btn[data-preset="due"] {
    --ring-color: #f59e0b;
}

.preset-btn[data-preset="beginner"] {
    --ring-color: #3b82f6;
}

.preset-btn[data-preset="all"] {
    --ring-color: #10b981;
}

.preset-btn.ring-2 {
    box-shadow: 0 0 0 2px var(--ring-color, #6366f1);
}

.preset-btn.ring-offset-2 {
    box-shadow: 0 0 0 2px white, 0 0 0 4px var(--ring-color, #6366f1);
}

/* 语音播放按钮样式 */
.audio-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.audio-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.audio-btn:active {
    transform: scale(0.95);
}

.audio-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.audio-btn:hover::before {
    left: 100%;
}

/* 语音播放动画 */
.audio-btn.playing {
    animation: audioPlaying 1s infinite;
}

@keyframes audioPlaying {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    50% {
        box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
    }
}

/* 语音设置样式 */
.speech-settings {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid #bae6fd;
    border-radius: 12px;
    padding: 1rem;
}

.speech-control {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.speech-control:hover {
    border-color: #3b82f6;
    background-color: #f8fafc;
}

/* 音量滑块样式 */
input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    height: 6px;
    background: #e5e7eb;
    border-radius: 3px;
    outline: none;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    background: #3b82f6;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
}

input[type="range"]::-webkit-slider-thumb:hover {
    background: #2563eb;
    transform: scale(1.1);
}

input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: #3b82f6;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;
}

input[type="range"]::-moz-range-thumb:hover {
    background: #2563eb;
    transform: scale(1.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .filter-group {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group label {
        justify-content: center;
    }

    .review-mode-card {
        text-align: center;
    }

    .preset-btn {
        padding: 0.75rem;
        font-size: 0.8rem;
    }

    .audio-btn {
        min-width: 40px;
        min-height: 40px;
    }

    .speech-control {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }
}

/* 进度条样式 */
.progress-bar {
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-image: linear-gradient(
        -45deg,
        rgba(255, 255, 255, .2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, .2) 50%,
        rgba(255, 255, 255, .2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 50px 50px;
    animation: move 2s linear infinite;
}

@keyframes move {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 50px 50px;
    }
}

/* 输入框样式增强 */
.input-enhanced {
    position: relative;
    transition: all 0.3s ease;
}

.input-enhanced:focus {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15);
}

/* 卡片阴影效果 */
.card-shadow {
    box-shadow: 
        0 1px 3px rgba(0, 0, 0, 0.12),
        0 1px 2px rgba(0, 0, 0, 0.24);
    transition: all 0.3s cubic-bezier(.25,.8,.25,1);
}

.card-shadow:hover {
    box-shadow: 
        0 14px 28px rgba(0, 0, 0, 0.25),
        0 10px 10px rgba(0, 0, 0, 0.22);
}

/* 统计卡片样式 */
.stats-card {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* 消息提示样式 */
.message-toast {
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 模态框样式 */
.modal-backdrop {
    backdrop-filter: blur(4px);
}

.modal-content {
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 加载动画 */
.loading-spinner {
    border: 3px solid #f3f4f6;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 640px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .mode-card {
        padding: 1rem;
    }
    
    .choice-option {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .grid-cols-3 {
        grid-template-columns: 1fr;
    }
    
    .flex-wrap {
        flex-direction: column;
        align-items: stretch;
    }
    
    .flex-wrap > * {
        margin-bottom: 0.5rem;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .dark-mode {
        background-color: #1f2937;
        color: #f9fafb;
    }
    
    .dark-mode .bg-white {
        background-color: #374151;
    }
    
    .dark-mode .text-gray-800 {
        color: #f9fafb;
    }
    
    .dark-mode .border-gray-300 {
        border-color: #4b5563;
    }
}

/* 打印样式 */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    .card-shadow {
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .btn-primary {
        border: 2px solid #000;
    }
    
    .choice-option {
        border-width: 2px;
    }
    
    .progress-bar {
        border: 1px solid #000;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
