<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenRouter配置调试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="js/llm-ocr.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold mb-6 text-center">OpenRouter配置调试工具</h1>
        
        <!-- 配置输入 -->
        <div class="mb-6 p-4 bg-blue-50 rounded-lg">
            <h2 class="text-lg font-semibold mb-3">配置输入</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-1">API Key:</label>
                    <input type="text" id="apiKey" placeholder="sk-or-v1-..." class="w-full px-3 py-2 border rounded-lg">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-1">Base URL:</label>
                    <input type="text" id="baseUrl" value="https://openrouter.ai/api/v1" class="w-full px-3 py-2 border rounded-lg">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-1">模型:</label>
                    <select id="model" class="w-full px-3 py-2 border rounded-lg">
                        <option value="meta-llama/llama-3.2-11b-vision-instruct:free">Llama 3.2 Vision (免费)</option>
                        <option value="google/gemini-flash-1.5">Gemini Flash 1.5 (免费)</option>
                    </select>
                </div>
            </div>
            <div class="mt-4">
                <button id="saveConfig" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg mr-2">
                    保存配置
                </button>
                <button id="loadConfig" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg mr-2">
                    加载配置
                </button>
                <button id="validateConfig" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg mr-2">
                    验证配置
                </button>
                <button id="testRecognition" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg">
                    测试识别
                </button>
            </div>
        </div>

        <!-- 状态显示 -->
        <div class="mb-6">
            <h2 class="text-lg font-semibold mb-3">状态信息</h2>
            <div id="status" class="bg-gray-50 p-4 rounded-lg min-h-32">
                <p class="text-gray-500">点击按钮查看状态...</p>
            </div>
        </div>

        <!-- 配置详情 -->
        <div class="mb-6">
            <h2 class="text-lg font-semibold mb-3">当前配置详情</h2>
            <div id="configDetails" class="bg-gray-50 p-4 rounded-lg">
                <pre id="configJson" class="text-sm text-gray-600">点击"加载配置"查看详情</pre>
            </div>
        </div>

        <!-- 测试图片 -->
        <div class="mb-6 hidden" id="testImageSection">
            <h2 class="text-lg font-semibold mb-3">测试图片</h2>
            <canvas id="testCanvas" width="300" height="150" class="border rounded"></canvas>
        </div>

        <!-- 识别结果 -->
        <div id="resultSection" class="hidden">
            <h2 class="text-lg font-semibold mb-3">识别结果</h2>
            <div id="result" class="bg-gray-50 p-4 rounded-lg">
            </div>
        </div>
    </div>

    <script>
        let llmManager;

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            llmManager = window.llmOcrManager;
            
            // 事件监听
            document.getElementById('saveConfig').addEventListener('click', saveConfig);
            document.getElementById('loadConfig').addEventListener('click', loadConfig);
            document.getElementById('validateConfig').addEventListener('click', validateConfig);
            document.getElementById('testRecognition').addEventListener('click', testRecognition);
            
            // 自动加载配置
            loadConfig();
        });

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const colors = {
                'success': 'text-green-600 bg-green-50 border border-green-200',
                'error': 'text-red-600 bg-red-50 border border-red-200',
                'info': 'text-blue-600 bg-blue-50 border border-blue-200',
                'warning': 'text-yellow-600 bg-yellow-50 border border-yellow-200'
            };
            
            statusDiv.innerHTML = `<div class="p-3 rounded ${colors[type]}">${message}</div>`;
        }

        function saveConfig() {
            const config = {
                apiKey: document.getElementById('apiKey').value.trim(),
                baseUrl: document.getElementById('baseUrl').value.trim(),
                model: document.getElementById('model').value.trim()
            };

            try {
                llmManager.updateApiConfig('openrouter', config);
                showStatus(`✅ 配置保存成功！<br>API Key: ${config.apiKey ? config.apiKey.substring(0, 20) + '...' : '未设置'}<br>模型: ${config.model}`, 'success');
                loadConfig(); // 重新加载显示
            } catch (error) {
                showStatus(`❌ 配置保存失败: ${error.message}`, 'error');
            }
        }

        function loadConfig() {
            try {
                const config = llmManager.apiConfigs.openrouter;
                
                // 更新UI
                document.getElementById('apiKey').value = config.apiKey || '';
                document.getElementById('baseUrl').value = config.baseUrl || 'https://openrouter.ai/api/v1';
                document.getElementById('model').value = config.model || 'meta-llama/llama-3.2-11b-vision-instruct:free';
                
                // 显示配置详情
                document.getElementById('configJson').textContent = JSON.stringify(config, null, 2);
                
                showStatus(`✅ 配置加载成功！<br>API Key: ${config.apiKey ? '已设置 (' + config.apiKey.length + ' 字符)' : '未设置'}<br>模型: ${config.model || '默认'}`, 'success');
            } catch (error) {
                showStatus(`❌ 配置加载失败: ${error.message}`, 'error');
            }
        }

        function validateConfig() {
            try {
                const isValid = llmManager.validateConfig('openrouter');
                const config = llmManager.apiConfigs.openrouter;
                
                if (isValid) {
                    showStatus(`✅ 配置验证通过！<br>API Key: 有效<br>模型: ${config.model}<br>Base URL: ${config.baseUrl}`, 'success');
                } else {
                    showStatus(`❌ 配置验证失败！<br>API Key: ${config.apiKey ? '已设置但可能无效' : '未设置'}<br>请检查配置是否正确`, 'error');
                }
            } catch (error) {
                showStatus(`❌ 验证过程出错: ${error.message}`, 'error');
            }
        }

        async function testRecognition() {
            try {
                // 生成测试图片
                const canvas = document.getElementById('testCanvas');
                const ctx = canvas.getContext('2d');
                
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                ctx.fillStyle = 'black';
                ctx.font = '16px Arial';
                ctx.fillText('Hello World', 20, 40);
                ctx.fillText('你好世界', 20, 80);
                ctx.fillText('apple 苹果', 20, 120);
                
                document.getElementById('testImageSection').classList.remove('hidden');
                
                const imageBase64 = canvas.toDataURL();
                
                showStatus('🔄 正在测试识别...', 'info');
                
                // 测试识别
                const result = await llmManager.recognize('openrouter', imageBase64);
                
                document.getElementById('result').innerHTML = `
                    <h4 class="font-bold text-green-600 mb-2">✅ 识别成功！</h4>
                    <p><strong>使用模型:</strong> ${llmManager.apiConfigs.openrouter.model}</p>
                    <p><strong>识别结果:</strong></p>
                    <div class="bg-white p-3 rounded border mt-2 font-mono text-sm whitespace-pre-wrap">${result}</div>
                `;
                document.getElementById('resultSection').classList.remove('hidden');
                
                showStatus('✅ 识别测试完成！查看下方结果。', 'success');
                
            } catch (error) {
                showStatus(`❌ 识别测试失败: ${error.message}`, 'error');
                
                document.getElementById('result').innerHTML = `
                    <h4 class="font-bold text-red-600 mb-2">❌ 识别失败</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                    <div class="mt-2 text-sm">
                        <p><strong>可能原因:</strong></p>
                        <ul class="list-disc list-inside">
                            <li>API Key无效或未设置</li>
                            <li>网络连接问题</li>
                            <li>模型不可用</li>
                            <li>配置格式错误</li>
                        </ul>
                    </div>
                `;
                document.getElementById('resultSection').classList.remove('hidden');
            }
        }

        // 页面加载时显示调试信息
        window.addEventListener('load', () => {
            console.log('LLM OCR Manager:', window.llmOcrManager);
            console.log('OpenRouter Config:', window.llmOcrManager?.apiConfigs?.openrouter);
        });
    </script>
</body>
</html>
