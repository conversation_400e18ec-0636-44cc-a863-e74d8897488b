# AI OCR识别配置指南

## 概述

新版本的OCR功能支持多种识别引擎，包括本地Tesseract和多种AI云服务。AI服务对手写体的识别效果远优于传统OCR。

## 支持的识别引擎

### 1. Tesseract (本地)
- **优点**：免费、隐私保护、无需网络
- **缺点**：手写体识别效果较差
- **适用场景**：印刷体文字、隐私敏感内容

### 2. 百度AI OCR (推荐)
- **优点**：手写体识别效果优秀、中文支持好
- **缺点**：需要API密钥、有使用限制
- **适用场景**：手写笔记、中文文档

### 3. 腾讯云OCR
- **优点**：性价比高、识别准确率不错
- **缺点**：需要API密钥、配置复杂
- **适用场景**：批量处理、企业应用

### 4. Azure Computer Vision
- **优点**：技术先进、多语言支持
- **缺点**：需要API密钥、国外服务
- **适用场景**：国际化应用、英文文档

## 快速开始

### 演示模式
1. 选择"百度AI (推荐)"引擎
2. 在API Key输入框中输入 `demo`
3. 在Secret Key输入框中输入 `demo`
4. 上传图片并开始识别

> 演示模式会返回预设的识别结果，用于测试功能

## API配置指南

### 百度AI OCR配置

#### 1. 注册百度智能云账号
- 访问：https://cloud.baidu.com/
- 注册并完成实名认证

#### 2. 创建OCR应用
1. 进入控制台 → 产品服务 → 人工智能 → 文字识别OCR
2. 点击"立即使用" → "创建应用"
3. 填写应用信息：
   - 应用名称：自定义
   - 应用类型：选择合适类型
   - 应用描述：可选

#### 3. 获取API密钥
1. 在应用列表中找到创建的应用
2. 记录 `API Key` 和 `Secret Key`
3. 在应用中输入这两个密钥

#### 4. API调用限制
- 免费额度：每月1000次
- 付费版本：按调用次数计费
- 详细价格：https://cloud.baidu.com/product/ocr/price

### 腾讯云OCR配置

#### 1. 注册腾讯云账号
- 访问：https://cloud.tencent.com/
- 注册并完成实名认证

#### 2. 开通OCR服务
1. 进入控制台 → 产品 → 人工智能 → 文字识别OCR
2. 点击"立即使用"开通服务

#### 3. 获取API密钥
1. 访问：https://console.cloud.tencent.com/cam/capi
2. 记录 `SecretId` 和 `SecretKey`
3. 在应用中输入这两个密钥

#### 4. API调用限制
- 免费额度：每月1000次
- 付费版本：按调用次数计费

### Azure Computer Vision配置

#### 1. 注册Azure账号
- 访问：https://azure.microsoft.com/
- 注册并获得免费额度

#### 2. 创建Computer Vision资源
1. 登录Azure门户
2. 创建资源 → AI + 机器学习 → Computer Vision
3. 填写基本信息并创建

#### 3. 获取API密钥
1. 进入创建的Computer Vision资源
2. 在"密钥和终结点"页面获取：
   - `Key 1` 或 `Key 2`（任选其一）
   - `Endpoint`（终结点URL）

#### 4. API调用限制
- 免费层：每月20,000次交易
- 付费版本：按调用次数计费

## 使用技巧

### 提高识别准确率
1. **图片质量**：确保图片清晰、光线充足
2. **文字角度**：保持文字水平，避免倾斜
3. **背景简洁**：避免复杂背景和干扰元素
4. **字体大小**：确保文字足够大，建议12号字以上

### 手写体识别建议
1. **字迹工整**：尽量使用工整的手写字体
2. **笔迹清晰**：使用深色笔迹，避免浅色或模糊
3. **间距适当**：字与字之间保持适当间距
4. **行距清晰**：行与行之间有明显分隔

### 成本控制
1. **选择合适引擎**：根据需求选择最经济的方案
2. **批量处理**：一次处理多个词汇，减少API调用次数
3. **本地预处理**：先用Tesseract尝试，失败再用AI服务
4. **监控用量**：定期检查API使用量，避免超额

## 故障排除

### 常见问题

#### Q: API密钥配置后仍然提示未配置？
A: 
1. 检查密钥是否正确复制（注意空格）
2. 确保密钥有效且未过期
3. 刷新页面重新配置

#### Q: 识别失败，提示网络错误？
A: 
1. 检查网络连接
2. 确认API服务状态正常
3. 检查是否超出API调用限制

#### Q: 识别结果不准确？
A: 
1. 检查图片质量
2. 尝试不同的识别引擎
3. 手动编辑识别结果

#### Q: CORS错误？
A: 
- 某些API服务不支持直接从浏览器调用
- 建议使用后端代理服务
- 或使用官方SDK

### 技术限制

1. **浏览器限制**：部分API服务不支持跨域调用
2. **文件大小**：建议图片大小不超过4MB
3. **格式支持**：支持JPG、PNG、GIF、BMP等常见格式
4. **语言支持**：主要支持中文和英文

## 开发者信息

### 后端代理示例

如果需要避免CORS问题，可以创建后端代理：

```javascript
// Node.js Express示例
app.post('/api/baidu-ocr', async (req, res) => {
    try {
        const response = await fetch('https://aip.baidubce.com/rest/2.0/ocr/v1/handwriting', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: new URLSearchParams(req.body)
        });
        
        const result = await response.json();
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});
```

### 自定义识别引擎

可以通过修改 `ai-ocr.js` 添加其他识别服务：

```javascript
async recognizeWithCustom(imageBase64) {
    // 实现自定义识别逻辑
    const response = await fetch('your-api-endpoint', {
        method: 'POST',
        body: JSON.stringify({ image: imageBase64 })
    });
    
    return await response.text();
}
```

## 更新日志

### v1.1.0
- 新增多引擎支持
- 添加百度AI OCR
- 添加腾讯云OCR
- 添加Azure Computer Vision
- 优化用户界面
- 添加配置管理

---

**享受AI识别带来的便利！** 🤖✨
